{"__meta": {"id": "01K0BB61BDKA8AQZY1Q607DTB1", "datetime": "2025-07-17 11:44:47", "utime": **********.855053, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752727486.767124, "end": **********.855135, "duration": 1.0880110263824463, "duration_str": "1.09s", "measures": [{"label": "Booting", "start": 1752727486.767124, "relative_start": 0, "end": **********.492706, "relative_end": **********.492706, "duration": 0.****************, "duration_str": "726ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.492722, "relative_start": 0.****************, "end": **********.85514, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "362ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.693363, "relative_start": 0.***************, "end": **********.698378, "relative_end": **********.698378, "duration": 0.005015134811401367, "duration_str": "5.02ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament::components.section.index", "start": **********.845546, "relative_start": 1.****************, "end": **********.845546, "relative_end": **********.845546, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.section.heading", "start": **********.847043, "relative_start": 1.****************, "end": **********.847043, "relative_end": **********.847043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.848848, "relative_start": 1.0817241668701172, "end": **********.848848, "relative_end": **********.848848, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.850934, "relative_start": 1.0838100910186768, "end": **********.852043, "relative_end": **********.852043, "duration": 0.0011088848114013672, "duration_str": "1.11ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 41314440, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "filament::components.section.index", "param_count": null, "params": [], "start": **********.845518, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/index.blade.phpfilament::components.section.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.section.heading", "param_count": null, "params": [], "start": **********.847022, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/heading.blade.phpfilament::components.section.heading", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}}, {"name": "filament::components.grid.column", "param_count": null, "params": [], "start": **********.848819, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}}]}, "queries": {"count": 37, "nb_statements": 37, "nb_visible_statements": 37, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.04007999999999999, "accumulated_duration_str": "40.08ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.736006, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 7.984}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.757287, "duration": 0.00263, "duration_str": "2.63ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 7.984, "width_percent": 6.562}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.761675, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 14.546, "width_percent": 1.946}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.764581, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 16.492, "width_percent": 5.763}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.7685602, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 22.255, "width_percent": 3.393}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.771617, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 25.649, "width_percent": 6.262}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.775199, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 31.911, "width_percent": 1.871}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.777045, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 33.782, "width_percent": 1.796}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.7795658, "duration": 0.00165, "duration_str": "1.65ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 35.579, "width_percent": 4.117}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.7822092, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 39.696, "width_percent": 1.672}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.783987, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 41.367, "width_percent": 2.869}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.786506, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 44.237, "width_percent": 2.645}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.788564, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 46.881, "width_percent": 1.771}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.790243, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 48.653, "width_percent": 0.973}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.791432, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 49.626, "width_percent": 1.272}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.793108, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 50.898, "width_percent": 2.221}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.79503, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 53.119, "width_percent": 1.098}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.7962382, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 54.217, "width_percent": 1.098}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.797434, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 55.314, "width_percent": 1.148}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.798962, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 56.462, "width_percent": 2.57}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.801245, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 59.032, "width_percent": 2.495}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.803214, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 61.527, "width_percent": 1.821}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.804985, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 63.348, "width_percent": 1.322}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.806777, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 64.671, "width_percent": 3.044}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8089862, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 67.715, "width_percent": 1.547}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8107839, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 69.261, "width_percent": 1.497}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.812417, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 70.758, "width_percent": 2.769}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8147402, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 73.528, "width_percent": 2.445}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8168151, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 75.973, "width_percent": 2.021}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.818696, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 77.994, "width_percent": 1.722}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.820617, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 79.716, "width_percent": 3.169}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8229642, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 82.884, "width_percent": 1.572}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.824952, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 84.456, "width_percent": 4.89}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.82913, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 89.346, "width_percent": 3.718}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-17'", "type": "query", "params": [], "bindings": ["2025-07-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.831632, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 93.064, "width_percent": 1.472}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-17'", "type": "query", "params": [], "bindings": ["2025-07-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8332012, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 94.536, "width_percent": 2.969}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-17'", "type": "query", "params": [], "bindings": ["2025-07-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.8355331, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 97.505, "width_percent": 2.495}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.treatments-chart #hLKXlKvwoQugUywV0hLT": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"d487b41032c70ca2163627b33ab14c8b\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.treatments-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\TreatmentsChart\"\n  \"id\" => \"hLKXlKvwoQugUywV0hLT\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Widgets\\TreatmentsChart@updateChartData<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/widgets/src/ChartWidget.php:100-109</a>", "middleware": "web", "duration": "1.09s", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-519129504 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-519129504\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1916293974 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"331 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;d487b41032c70ca2163627b33ab14c8b&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;hLKXlKvwoQugUywV0hLT&quot;,&quot;name&quot;:&quot;app.filament.widgets.treatments-chart&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;vi&quot;},&quot;checksum&quot;:&quot;1fb4cb7031762f738bf6e4f51ea9338becbc4bb11ad84afadd4930f7c17186cf&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1916293974\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1483299520 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">582</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkVHdVZFMENrajFsM3E0K0xQL3NnOHc9PSIsInZhbHVlIjoiQXNyREY0Uk01dzhraTY2cTdhTWE2VlorbVUxY2hMb2owYzNYeTg4Y0xBdE9Pcm9weDJGc0NyZ284c2NXcHY5TUJlMmJTZGVVOXZIT1hLVjlVcUFZZkZlSUwxbW5pK3MxT2JjMG4zUFA4TWhWMWhHY1F2ZFFNWFExV3JoN2ljTXUiLCJtYWMiOiJmY2I0OWNiOTU0NDc3ZDM0NzUxZTE3MDQxN2YzYmY4ZTQ0NjY4ZDAyNGZlOGQ0ZDg0MmJmNjI4MjFlOWY5NDI5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imc0ZVBBMTVoZXdKTEt0ZVlOVFBWZ3c9PSIsInZhbHVlIjoieFEydUgrb25ZUndyd3piT2ZIQWEvdStwcm1PSjBjY3hZb1ZjVm41ZDhVdzM5L0xrUHQ1OURMQ0lJRU92YXMyT3RxdkIrRkUvWjh5TEw4REVHUU9kVW1aZm93RlduYUJudWswcTRxZnFDSkJTSUVkaS9jQzRGYkZ1enRibUZCc3MiLCJtYWMiOiIxMDc0YzZhZTA5OTBhODk0MTBlM2E0ZDMxNmM4MmQ3Zjk0YzQ3YWQ2MmNjZjA4MWVjZjVkYmJiNDU1YjllMzFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1483299520\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1206506188 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ALiu3MXg2qeWtslysnYzKUjGVbJ1TKvwt3KhqDy1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1206506188\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-892876203 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 04:44:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-892876203\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1271773239 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://auvista.test/admin/menus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$2uJGkMnwk7Lj203GgLThU.EI1Z7VNTd1oXu5zgFDqiWVi2sEnDYEy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1271773239\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}