{"__meta": {"id": "01K0C85SNY234N91T4MBYJHE7D", "datetime": "2025-07-17 20:11:28", "utime": **********.702639, "method": "GET", "uri": "/auvista/public/settings/logo.png", "ip": "::1"}, "messages": {"count": 1, "messages": [{"message": "[20:11:28] LOG.info: CartPopup loadCart called: {\n    \"cartItems\": [],\n    \"session_cart\": [],\n    \"total\": 0,\n    \"session_id\": \"QPCUmJWtgOSFfYsagPQ0lgueMdgOIi6WU8R31mKI\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.672728, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.131577, "end": **********.70267, "duration": 0.5710930824279785, "duration_str": "571ms", "measures": [{"label": "Booting", "start": **********.131577, "relative_start": 0, "end": **********.466347, "relative_end": **********.466347, "duration": 0.*****************, "duration_str": "335ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.466359, "relative_start": 0.****************, "end": **********.702672, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "236ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.60578, "relative_start": 0.*****************, "end": **********.702681, "relative_end": 1.0967254638671875e-05, "duration": 0.*****************, "duration_str": "96.9ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: errors::404", "start": **********.616767, "relative_start": 0.*****************, "end": **********.616767, "relative_end": **********.616767, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.layouts.default", "start": **********.617118, "relative_start": 0.*****************, "end": **********.617118, "relative_end": **********.617118, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.components.product_styles", "start": **********.637273, "relative_start": 0.**************54, "end": **********.637273, "relative_end": **********.637273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.header", "start": **********.637513, "relative_start": 0.5059359073638916, "end": **********.637513, "relative_end": **********.637513, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.cart-count", "start": **********.646955, "relative_start": 0.5153779983520508, "end": **********.646955, "relative_end": **********.646955, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.cart-count", "start": **********.668074, "relative_start": 0.5364968776702881, "end": **********.668074, "relative_end": **********.668074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.breadcrumb", "start": **********.668887, "relative_start": 0.5373098850250244, "end": **********.668887, "relative_end": **********.668887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: H:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "start": **********.67316, "relative_start": 0.5415830612182617, "end": **********.67316, "relative_end": **********.67316, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.footer", "start": **********.676416, "relative_start": 0.****************, "end": **********.676416, "relative_end": **********.676416, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": ********, "peak_usage_str": "40MB"}, "exceptions": {"count": 1, "exceptions": [{"type": "Symfony\\Component\\HttpKernel\\Exception\\NotFoundHttpException", "message": "The route settings/logo.png could not be found.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Routing/AbstractRouteCollection.php", "line": 45, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:31</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"67 characters\">vendor/laravel/framework/src/Illuminate/Routing/RouteCollection.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>162</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"18 characters\">handleMatchedRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Routing\\AbstractRouteCollection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-const>null</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>763</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">match</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">Illuminate\\Routing\\RouteCollection</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>750</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"9 characters\">findRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>739</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>201</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>170</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>110</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"94 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>209</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"61 characters\">Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>127</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>176</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>145</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Application.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>1220</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>17</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"13 characters\">handleRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Application</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return $this->getRouteForMethods($request, $others);\n", "        }\n", "\n", "        throw new NotFoundHttpException(sprintf(\n", "            'The route %s could not be found.',\n", "            $request->path()\n", "        ));\n"], "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FAbstractRouteCollection.php&line=45", "ajax": false, "filename": "AbstractRouteCollection.php", "line": "45"}}]}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 9, "nb_templates": 9, "templates": [{"name": "errors::404", "param_count": null, "params": [], "start": **********.616749, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/errors/404.blade.phperrors::404", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ferrors%2F404.blade.php&line=1", "ajax": false, "filename": "404.blade.php", "line": "?"}}, {"name": "templates.auvista.layouts.default", "param_count": null, "params": [], "start": **********.617107, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/layouts/default.blade.phptemplates.auvista.layouts.default", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Flayouts%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "templates.auvista.components.product_styles", "param_count": null, "params": [], "start": **********.637258, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/components/product_styles.blade.phptemplates.auvista.components.product_styles", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fcomponents%2Fproduct_styles.blade.php&line=1", "ajax": false, "filename": "product_styles.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.header", "param_count": null, "params": [], "start": **********.637502, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.phptemplates.auvista.blocks.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "livewire.cart-count", "param_count": null, "params": [], "start": **********.64694, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-count.blade.phplivewire.cart-count", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-count.blade.php&line=1", "ajax": false, "filename": "cart-count.blade.php", "line": "?"}}, {"name": "livewire.cart-count", "param_count": null, "params": [], "start": **********.66806, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-count.blade.phplivewire.cart-count", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-count.blade.php&line=1", "ajax": false, "filename": "cart-count.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.breadcrumb", "param_count": null, "params": [], "start": **********.668874, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/breadcrumb.blade.phptemplates.auvista.blocks.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "H:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "param_count": null, "params": [], "start": **********.673147, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.phpH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-popup.blade.php&line=1", "ajax": false, "filename": "cart-popup.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.footer", "param_count": null, "params": [], "start": **********.676384, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.phptemplates.auvista.blocks.footer", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "queries": {"count": 38, "nb_statements": 38, "nb_visible_statements": 38, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.026649999999999993, "accumulated_duration_str": "26.65ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `settings` where `name` = 'site_description' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_description", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.617816, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 4.615}, {"sql": "select * from `settings` where `name` = 'site_keywords' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_keywords", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.621367, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 4.615, "width_percent": 3.227}, {"sql": "select * from `settings` where `name` = 'site_keywords' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_keywords", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.622869, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "helpers.php:21", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=21", "ajax": false, "filename": "helpers.php", "line": "21"}, "connection": "auvista", "explain": null, "start_percent": 7.842, "width_percent": 1.538}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.623817, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 9.381, "width_percent": 1.201}, {"sql": "select * from `settings` where `name` = 'site_title' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_title", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.624728, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 10.582, "width_percent": 1.163}, {"sql": "select * from `settings` where `name` = 'site_description' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_description", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6256042, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 11.745, "width_percent": 1.163}, {"sql": "select * from `settings` where `name` = 'og_image' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["og_image", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6264799, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 12.908, "width_percent": 2.064}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.627697, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 14.972, "width_percent": 3.077}, {"sql": "select * from `settings` where `name` = 'site_title' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_title", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.629696, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 18.049, "width_percent": 2.777}, {"sql": "select * from `settings` where `name` = 'site_description' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_description", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.631078, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 20.826, "width_percent": 2.326}, {"sql": "select * from `settings` where `name` = 'og_image' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["og_image", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6325228, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 23.152, "width_percent": 2.214}, {"sql": "select * from `settings` where `name` = 'site_title' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_title", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.63377, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 25.366, "width_percent": 1.839}, {"sql": "select * from `settings` where `name` = 'site_logo' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_logo", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.63504, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 27.205, "width_percent": 3.79}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.638682, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 30.994, "width_percent": 2.702}, {"sql": "select * from `menus` where `location` = 'main-menu' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["main-menu", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.650024, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 33.696, "width_percent": 4.203}, {"sql": "select * from `menu_items` where `menu_id` = 5 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.6522908, "duration": 0.00094, "duration_str": "940μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 37.899, "width_percent": 3.527}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (17, 18, 28, 29, 30, 31, 32, 67) and `status` = 1 order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 21, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.655101, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 41.426, "width_percent": 4.39}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (19, 24, 25, 26, 27) and `status` = 1 order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 26, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.657412, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 25, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 45.816, "width_percent": 3.002}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (20, 21, 22, 23) and `status` = 1 order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 30, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 31, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.658918, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 30, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 48.818, "width_percent": 1.989}, {"sql": "select * from `brands` where `brands`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 32}, {"index": 32, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 30}, {"index": 33, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.663739, "duration": 0.00122, "duration_str": "1.22ms", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 50.807, "width_percent": 4.578}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.66604, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 55.385, "width_percent": 1.989}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.677471, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 57.373, "width_percent": 3.227}, {"sql": "select * from `settings` where `name` = 'company_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["company_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.679032, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 60.6, "width_percent": 1.876}, {"sql": "select * from `settings` where `name` = 'company_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["company_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.680016, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "helpers.php:21", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=21", "ajax": false, "filename": "helpers.php", "line": "21"}, "connection": "auvista", "explain": null, "start_percent": 62.477, "width_percent": 1.689}, {"sql": "select * from `settings` where `name` = 'contact_address' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["contact_address", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.680993, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 64.165, "width_percent": 1.651}, {"sql": "select * from `settings` where `name` = 'office_address' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["office_address", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6819701, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 65.816, "width_percent": 1.689}, {"sql": "select * from `settings` where `name` = 'contact_email' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["contact_email", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6830661, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 67.505, "width_percent": 3.189}, {"sql": "select * from `menus` where `location` = 'footer-support' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["footer-support", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.684848, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 70.694, "width_percent": 3.002}, {"sql": "select * from `menu_items` where `menu_id` = 6 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [6, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.686349, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 73.696, "width_percent": 2.664}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (33, 34, 35) and `status` = 1 order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 21, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.68777, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 76.36, "width_percent": 2.101}, {"sql": "select * from `menus` where `location` = 'footer-policy' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["footer-policy", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.689014, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 78.462, "width_percent": 2.026}, {"sql": "select * from `menu_items` where `menu_id` = 7 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [7, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.690275, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 80.488, "width_percent": 5.891}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (36, 37, 38, 39, 40) and `status` = 1 order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 21, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.692692, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 86.379, "width_percent": 2.402}, {"sql": "select * from `settings` where `name` = 'facebook_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["facebook_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.694155, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 88.78, "width_percent": 1.914}, {"sql": "select * from `settings` where `name` = 'instagram_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["instagram_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.695267, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 90.694, "width_percent": 1.951}, {"sql": "select * from `settings` where `name` = 'youtube_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["youtube_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.696415, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 92.645, "width_percent": 1.839}, {"sql": "select * from `settings` where `name` = 'site_copyright' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_copyright", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.697635, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 94.484, "width_percent": 3.49}, {"sql": "select * from `settings` where `name` = 'designer_credit' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["designer_credit", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.699274, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 97.974, "width_percent": 2.026}]}, "models": {"data": {"App\\Models\\MenuItem": {"value": 25, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Setting": {"value": 22, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "App\\Models\\Menu": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "App\\Models\\Brand": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}}, "count": 51, "is_counter": true}, "livewire": {"data": {"cart-count #NAOfWHfGEc4NsYjJL5Lv": "array:4 [\n  \"data\" => array:1 [\n    \"count\" => 0\n  ]\n  \"name\" => \"cart-count\"\n  \"component\" => \"App\\Livewire\\CartCount\"\n  \"id\" => \"NAOfWHfGEc4NsYjJL5Lv\"\n]", "cart-count #LJsd8w6yUo9XZlexcg82": "array:4 [\n  \"data\" => array:1 [\n    \"count\" => 0\n  ]\n  \"name\" => \"cart-count\"\n  \"component\" => \"App\\Livewire\\CartCount\"\n  \"id\" => \"LJsd8w6yUo9XZlexcg82\"\n]", "cart-popup #YEkIVoLNqd8nGMpIL4Vi": "array:4 [\n  \"data\" => array:2 [\n    \"cartItems\" => []\n    \"total\" => 0\n  ]\n  \"name\" => \"cart-popup\"\n  \"component\" => \"App\\Livewire\\CartPopup\"\n  \"id\" => \"YEkIVoLNqd8nGMpIL4Vi\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "404 Not Found", "full_url": "https://localhost/auvista/public/settings/logo.png", "action_name": null, "controller_action": null, "duration": "575ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-381354113 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-381354113\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1700786161 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1700786161\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1132149001 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/*********</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">cross-site</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">no-cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">image</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-storage-access</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">active</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">http://localhost/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">en-US,en;q=0.9,vi;q=0.8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1132149001\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1727972208 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1727972208\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1397185658 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 13:11:28 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1397185658\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-733798924 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-733798924\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "404 Not Found", "full_url": "https://localhost/auvista/public/settings/logo.png"}, "badge": "404 Not Found"}}