{"__meta": {"id": "01K0BB5J1MEQP2NGPAD1SBTETH", "datetime": "2025-07-17 11:44:32", "utime": **********.182495, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.387042, "end": **********.182706, "duration": 0.7956640720367432, "duration_str": "796ms", "measures": [{"label": "Booting", "start": **********.387042, "relative_start": 0, "end": **********.803947, "relative_end": **********.803947, "duration": 0.*****************, "duration_str": "417ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.803966, "relative_start": 0.*****************, "end": **********.182721, "relative_end": 1.4781951904296875e-05, "duration": 0.****************, "duration_str": "379ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.970203, "relative_start": 0.****************, "end": **********.976593, "relative_end": **********.976593, "duration": 0.006390094757080078, "duration_str": "6.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament::components.section.index", "start": **********.170649, "relative_start": 0.***************, "end": **********.170649, "relative_end": **********.170649, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.section.heading", "start": **********.17256, "relative_start": 0.****************, "end": **********.17256, "relative_end": **********.17256, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.173865, "relative_start": 0.*********286499, "end": **********.173865, "relative_end": **********.173865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.17687, "relative_start": 0.7898280620574951, "end": **********.178498, "relative_end": **********.178498, "duration": 0.0016279220581054688, "duration_str": "1.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 41292224, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "filament::components.section.index", "param_count": null, "params": [], "start": **********.170611, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/index.blade.phpfilament::components.section.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.section.heading", "param_count": null, "params": [], "start": **********.172535, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/heading.blade.phpfilament::components.section.heading", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}}, {"name": "filament::components.grid.column", "param_count": null, "params": [], "start": **********.173827, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}}]}, "queries": {"count": 37, "nb_statements": 37, "nb_visible_statements": 37, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.052240000000000016, "accumulated_duration_str": "52.24ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.027298, "duration": 0.005860000000000001, "duration_str": "5.86ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 11.217}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.062605, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 11.217, "width_percent": 6.126}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.068195, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 17.343, "width_percent": 2.661}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.072667, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 20.004, "width_percent": 2.852}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.0755348, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 22.856, "width_percent": 2.106}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.0782032, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 24.962, "width_percent": 2.891}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.081169, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 27.852, "width_percent": 3.178}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.084924, "duration": 0.00199, "duration_str": "1.99ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 31.03, "width_percent": 3.809}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.088084, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 34.839, "width_percent": 2.144}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.090897, "duration": 0.00441, "duration_str": "4.41ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 36.983, "width_percent": 8.442}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.098459, "duration": 0.00269, "duration_str": "2.69ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 45.425, "width_percent": 5.149}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.102429, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 50.574, "width_percent": 2.087}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.104929, "duration": 0.00159, "duration_str": "1.59ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 52.661, "width_percent": 3.044}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1077058, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 55.704, "width_percent": 1.417}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.109478, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 57.121, "width_percent": 1.129}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.111285, "duration": 0.00182, "duration_str": "1.82ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 58.25, "width_percent": 3.484}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.114702, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 61.734, "width_percent": 1.895}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.116742, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 63.629, "width_percent": 1.493}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.118745, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 65.123, "width_percent": 2.871}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.121409, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 67.994, "width_percent": 1.263}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1230712, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 69.257, "width_percent": 1.225}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.124724, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 70.482, "width_percent": 2.814}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.127357, "duration": 0.00083, "duration_str": "830μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 73.296, "width_percent": 1.589}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1291099, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 74.885, "width_percent": 1.129}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.130575, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 76.015, "width_percent": 1.149}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.132378, "duration": 0.0010400000000000001, "duration_str": "1.04ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 77.163, "width_percent": 1.991}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1345541, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 79.154, "width_percent": 1.608}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.136354, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 80.762, "width_percent": 1.263}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.138062, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 82.025, "width_percent": 1.129}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1397731, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 83.155, "width_percent": 2.278}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1420672, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 85.433, "width_percent": 1.531}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.144027, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 86.964, "width_percent": 2.163}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1467562, "duration": 0.00189, "duration_str": "1.89ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 89.127, "width_percent": 3.618}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1497428, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 92.745, "width_percent": 1.512}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-17'", "type": "query", "params": [], "bindings": ["2025-07-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.151561, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 94.257, "width_percent": 1.263}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-17'", "type": "query", "params": [], "bindings": ["2025-07-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.153667, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 95.521, "width_percent": 3.178}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-17'", "type": "query", "params": [], "bindings": ["2025-07-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.156308, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 98.698, "width_percent": 1.302}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.treatments-chart #hLKXlKvwoQugUywV0hLT": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"d487b41032c70ca2163627b33ab14c8b\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.treatments-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\TreatmentsChart\"\n  \"id\" => \"hLKXlKvwoQugUywV0hLT\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Widgets\\TreatmentsChart@updateChartData<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/widgets/src/ChartWidget.php:100-109</a>", "middleware": "web", "duration": "798ms", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-43545303 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-43545303\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1859806309 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"349 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;d487b41032c70ca2163627b33ab14c8b&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;hLKXlKvwoQugUywV0hLT&quot;,&quot;name&quot;:&quot;app.filament.widgets.treatments-chart&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;lazyLoaded&quot;:true,&quot;errors&quot;:[],&quot;locale&quot;:&quot;vi&quot;},&quot;checksum&quot;:&quot;62bec898cfdd6c0a0b32d7958b5e00d86c9dbda30ca7b65bf88f4ee5fcbab780&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1859806309\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1026876448 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">551</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IitUSDhtT2lMenFTc2xBSkhuSVo1VWc9PSIsInZhbHVlIjoiNGt3YkFlRWZ1eEt4VW9yQzAvWTNXL2c2MExTL2ZsYXU4WUN6VnRuQ25Xcy9hNmZrRWJrSVZmVWN2SzJoYU5vQ1Fhc1JSc0lDSU93NkVIS25pWXlhNDZSL0FlQTRSY1J3QUtybEs4RnY1N1NKdlk0M0ZEMzFDUDNCcVlUdllGWTkiLCJtYWMiOiIyMjc5NTYwODBjNTUwYWVlNGNiODRlZmE5Y2YwYTRmNDAwOTA4MzQxOWRjYjA4MjhjNWY5MjY5YTQ4ZDZkMzFkIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkhkZnBOZE9UTUZheHhqWklGTXFGRnc9PSIsInZhbHVlIjoiMFVDNUQzQnJuSHZvc3hnMjFhV2RyQXV1cHFiNlYwZUlpWnFZNHFCRm8xcGNRWHg5UFBmS290TVpxQTcrS2tQZWVheElXOXBQTXljYkQzMWh2STh3S2grYm5RQ3hxNnlCYzVLZTZwbFZVdzFmTlZ3TVRJWVJsR2h5bEI4NFZUTDMiLCJtYWMiOiJhNTkwNTliNTcwYzUzYjNjN2E1NDBiZGZkMDhiNzY4NzcwMmIwODg3YTA1ODEzMGU4MWEwNDFiMjQzZTQxNzM0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1026876448\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1643130169 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ALiu3MXg2qeWtslysnYzKUjGVbJ1TKvwt3KhqDy1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1643130169\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1409989262 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 04:44:32 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1409989262\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1355730698 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$2uJGkMnwk7Lj203GgLThU.EI1Z7VNTd1oXu5zgFDqiWVi2sEnDYEy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1355730698\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}