<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Category;
use App\Models\Post;
use Illuminate\Support\Facades\Log;
use App\Traits\SeoTrait;
use App\Helpers\TranslationHelper;
use Illuminate\Support\Facades\App;

class PostController extends BaseController
{
    use SeoTrait;

    /**
     * Hiển thị danh sách bài viết
     *
     * @return \Illuminate\View\View
     */

    /**
     * Hiển thị chi tiết bài viết
     *
     * @param string $slug Slug của bài viết
     * @return \Illuminate\View\View
     */
    public function detail($slug)
    {
        // Lấy chi tiết bài viết dựa trên slug, status và lang
        $post = Post::where('slug', $slug)
            ->where('status', 'published')
            ->where('lang', \Illuminate\Support\Facades\App::getLocale())
            ->firstOrFail();

        // Lấy danh mục chính của bài viết (nếu có)
        $primaryCategory = $post->categories()
            ->wherePivot('is_primary', true)
            ->first();

        if (!$primaryCategory) {
            // Nếu không có danh mục chính, lấy danh mục đầu tiên
            $primaryCategory = $post->categories()->first();
        }

        // Debug: Kiểm tra danh mục chính
        \Illuminate\Support\Facades\Log::info('Primary Category: ' . ($primaryCategory ? json_encode($primaryCategory->toArray()) : 'null'));

        // Lấy danh mục cha và danh mục gốc nếu có
        $parentCategory = null;
        $rootCategory = null;

        if ($primaryCategory) {
            // Nếu danh mục có parent_id, lấy thông tin danh mục cha
            if ($primaryCategory->parent_id) {
                $parentCategory = Category::find($primaryCategory->parent_id);

                // Nếu danh mục cha có parent_id, lấy thông tin danh mục gốc
                if ($parentCategory && $parentCategory->parent_id) {
                    $rootCategory = Category::find($parentCategory->parent_id);
                } else {
                    // Nếu danh mục cha không có parent_id, nó là danh mục gốc
                    $rootCategory = $parentCategory;
                    $parentCategory = null;
                }
            } else {
                // Nếu danh mục không có parent_id, nó là danh mục gốc
                $rootCategory = $primaryCategory;
            }
        }

        // Lấy các bài viết liên quan cùng danh mục (loại trừ bài viết hiện tại)
        $relatedPosts = collect();
        if ($primaryCategory) {
            $relatedPosts = Post::join('post_category', 'posts.id', '=', 'post_category.post_id')
                ->where('post_category.category_id', $primaryCategory->id)
                ->where('posts.id', '!=', $post->id)
                ->where('posts.lang', app()->getLocale())
                ->where('posts.status', 'published')
                ->with(['categories' => function ($query) {
                    $query->where('type', 'post');
                }])
                ->orderBy('posts.published_at', 'desc')
                ->limit(3)
                ->get(['posts.*']);
        }

        // Nếu không đủ 3 bài viết cùng danh mục, lấy thêm bài viết khác
        if ($relatedPosts->count() < 3) {
            $excludeIds = $relatedPosts->pluck('id')->toArray();
            $excludeIds[] = $post->id; // Loại trừ bài viết hiện tại

            $additionalPosts = Post::where('status', 'published')
                ->where('lang', app()->getLocale())
                ->whereNotIn('id', $excludeIds)
                ->with(['categories' => function ($query) {
                    $query->where('type', 'post');
                }])
                ->orderBy('published_at', 'desc')
                ->limit(3 - $relatedPosts->count())
                ->get();

            $relatedPosts = $relatedPosts->merge($additionalPosts);
        }

        // Lấy bài viết nổi bật cho sidebar (loại trừ bài viết hiện tại)
        $featuredPosts = Post::where('status', 'published')
            ->where('is_featured', true)
            ->where('id', '!=', $post->id)
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();

        // Nếu không đủ bài viết featured, lấy thêm bài viết mới nhất
        if ($featuredPosts->count() < 5) {
            $additionalPosts = Post::where('status', 'published')
                ->where('id', '!=', $post->id)
                ->whereNotIn('id', $featuredPosts->pluck('id'))
                ->where('lang', app()->getLocale())
                ->with(['categories' => function ($query) {
                    $query->where('type', 'post');
                }])
                ->orderBy('published_at', 'desc')
                ->limit(5 - $featuredPosts->count())
                ->get();

            $featuredPosts = $featuredPosts->merge($additionalPosts);
        }

        // Xử lý nội dung bài viết để thay thế URL ảnh bằng getImageUrl
        $post->content = processContentImages($post->content);

        // Tạo TOC từ nội dung bài viết
        $tocData = generateTocFromContent($post->content);
        $post->content = $tocData['content']; // Cập nhật content với các ID heading
        $tocItems = $tocData['toc'];

        // Chuẩn bị dữ liệu SEO
        $seoData = $this->getDataSeo([
            'seo_title' => $post->seo_title ?? $post->title,
            'seo_description' => $post->seo_description ?? $post->excerpt,
            'seo_keywords' => $post->seo_keywords ?? '',
            'seo_author' => $post->author->name ?? 'AuVista',
            'seo_canonical' => route('news.detail', $slug),
            'seo_image' => $post->image ?? ''
        ]);

        $data = [
            'pageTitle' => $post->title,
            'post' => $post,
            'primaryCategory' => $primaryCategory,
            'parentCategory' => $parentCategory,
            'rootCategory' => $rootCategory,
            'relatedPosts' => $relatedPosts,
            'featuredPosts' => $featuredPosts,
            'tocItems' => $tocItems
        ];

        // Tạo breadcrumbs theo định dạng đồng nhất
        $breadcrumbs = [
            'items' => [
                [
                    'name' => __('messages.home'),
                    'url' => route('home'),
                    'active' => false
                ],
            ],
            'current' => $post->title
        ];

        // Thêm các danh mục vào breadcrumbs nếu có
        if ($rootCategory && $rootCategory->id != ($parentCategory->id ?? null) && $rootCategory->id != ($primaryCategory->id ?? null)) {
            $breadcrumbs[] = ['title' => $rootCategory->name, 'url' => route('static.page', $rootCategory->slug)];
        }

        if ($parentCategory) {
            $breadcrumbs[] = ['title' => $parentCategory->name, 'url' => route('static.page', $parentCategory->slug)];
        }

        if ($primaryCategory) {
            $breadcrumbs[] = ['title' => $primaryCategory->name, 'url' => route('static.page', $primaryCategory->slug)];
        }

        // Thêm tiêu đề bài viết vào breadcrumbs
        $breadcrumbs[] = ['title' => $post->title];

        return view('templates.auvista.pages.news-detail', compact('data', 'breadcrumbs') + $seoData);
    }

    /**
     * Hiển thị danh mục bài viết
     *
     * @param string $categorySlug Slug của danh mục
     * @param int $page Trang hiện tại
     * @return \Illuminate\View\View
     */
    public function category($categorySlug, $page = 1)
    {
        // Lấy thông tin danh mục từ database dựa trên slug
        $category = Category::where('slug', $categorySlug)
            ->where('status', 'active')
            ->firstOrFail();
        if ($category->lang !== App::getLocale()) {
            $code = TranslationHelper::getTranslatedPageCode($category->id, 'category');
            $category = TranslationHelper::getTranslatedPage($code, App::getLocale(), 'category');
            return redirect()->route('static.page', $category->slug);
        }

        // Xác định số lượng bài viết mỗi trang
        $perPage = 10;

        // Truy vấn từ database với phân trang
        $posts = Post::join('post_category', 'posts.id', '=', 'post_category.post_id')
            ->where('post_category.category_id', $category->id)
            ->select(
                'posts.id',
                'posts.title',
                'posts.slug',
                'posts.excerpt',
                'posts.image',
                'posts.created_at',
                'posts.published_at'
            )
            ->where('posts.lang', app()->getLocale())
            ->where('posts.status', 'published')
            ->orderBy('posts.published_at', 'desc')
            ->paginate($perPage, ['*'], 'page', $page);

        // Chuẩn hóa dữ liệu bài viết
        $postItems = $posts->items();
        foreach ($postItems as $post) {
            // Chuẩn hóa định dạng ngày
            if (property_exists($post, 'created_at') && $post->created_at) {
                $post->created_at = date('d/m/Y', strtotime($post->created_at));
            }

            // Đảm bảo đường dẫn hình ảnh đúng
            if (property_exists($post, 'image') && $post->image) {
                // Nếu đường dẫn không có 'storage/' ở đầu, thêm vào
                if (strpos($post->image, 'storage/') !== 0 && strpos($post->image, '/storage/') !== 0) {
                    $post->image = 'storage/' . ltrim($post->image, '/');
                }
            }
        }

        // Lấy bài viết nổi bật (featured post) - loại trừ dự án
        $featuredPost = Post::where('status', 'published')
            ->where('is_featured', true)
            ->whereDoesntHave('categories', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->first();
        // Lấy các bài viết thường (không phải featured) - loại trừ dự án
        $regularPosts = Post::where('status', 'published')
            ->where('id', '!=', $featuredPost ? $featuredPost->id : 0)
            ->whereDoesntHave('categories', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->limit(4)
            ->get();
        // Lấy danh mục bài viết (không bao gồm dự án)
        $allPostCategories = Category::where('type', 'post')
            ->where('status', 'active')
            ->where('slug', '!=', 'du-an')
            ->where('lang', app()->getLocale())
            ->orderBy('name')
            ->get();
        // Lấy bài viết mới nhất cho sidebar - loại trừ dự án
        $recentPosts = Post::where('status', 'published')
            ->whereDoesntHave('categories', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();
        $hasMoreCategories = $allPostCategories->count() > 5;
        $allPostCategories = $allPostCategories->take(5);
        // Chuẩn bị dữ liệu SEO
        $seoData = $this->getDataSeo([
            'seo_title' => $category->seo_title ?? '',
            'seo_description' => $category->seo_description ?? '',
            'seo_keywords' => $category->seo_keywords ?? '',
            'seo_author' => $category->author ?? '',
            'seo_canonical' => route('static.page', $categorySlug),
            'seo_image' => $category->thumbnail ?? ''
        ]);

        $data = [
            'pageTitle' => $category->name,
            'category' => [
                'id' => $category->id,
                'name' => $category->name,
                'description' => $category->description,
                'slug' => $category->slug,
                'image' => $category->thumbnail,
                'type' => $categorySlug
            ],
            'posts' => $posts,
            'categorySlug' => $categorySlug,
            'featuredPost' => $featuredPost,
            'regularPosts' => $regularPosts,
            'postCategories' => $allPostCategories,
            'recentPosts' => $recentPosts,
            'hasMoreCategories' => $hasMoreCategories,
            'allPostCategories' => $allPostCategories
        ];

        // Tạo breadcrumbs
        $breadcrumbs = [
            'items' => [
                [
                    'name' => __('messages.home'),
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => $category->name,
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => $category->name
        ];

        // Thêm cờ đánh dấu nếu là trang dự án
        if ($categorySlug == 'du-an') {
            $data['is_project'] = true;

            // Kiểm tra nếu có tham số type trong URL, ví dụ: du-an?type=thang-may-gia-dinh
            if (request()->has('type')) {
                $projectType = request()->get('type');
                // Lọc các bài đăng theo loại dự án (có thể thực hiện theo tag hoặc meta)
                // Ở đây chỉ thêm thông tin loại để hiển thị tab đúng
                $data['project_type'] = $projectType;
            }
        }

        // Sử dụng template chung cho tất cả các danh mục
        \Illuminate\Support\Facades\Log::info('Using common category template for: ' . $categorySlug);
        return view($this->getViewPath('post.category'), compact('data', 'breadcrumbs') + $seoData);
    }

    /**
     * Tìm kiếm bài viết
     *
     * @param Request $request
     * @return \Illuminate\View\View
     */
    public function search(Request $request)
    {
        $keyword = $request->input('keyword', '');

        // Truy vấn bài viết theo từ khóa
        $posts = Post::where('title', 'like', "%{$keyword}%")
            ->orWhere('content', 'like', "%{$keyword}%")
            ->where('lang', app()->getLocale())
            ->where('status', 'published')
            ->orderBy('published_at', 'desc')
            ->paginate(10);

        // Chuẩn bị dữ liệu SEO
        $seoData = $this->getDataSeo([
            'seo_title' => 'Tìm kiếm: ' . $keyword,
            'seo_description' => 'Kết quả tìm kiếm cho từ khóa: ' . $keyword,
            'seo_keywords' => $keyword . ', tìm kiếm bài viết',
            'seo_author' => getSetting('site_name'),
            'seo_canonical' => route('post.search', ['q' => $keyword]),
            'seo_image' => getSetting('site_logo')
        ]);

        $data = [
            'pageTitle' => 'Kết quả tìm kiếm: ' . $keyword,
            'keyword' => $keyword,
            'posts' => $posts,
            'total' => $posts->total()
        ];

        $data['pagination'] = [
            'previous' => $posts->previousPageUrl(),
            'next' => $posts->nextPageUrl(),
            'pages' => []
        ];

        foreach (range(1, $posts->lastPage()) as $page) {
            $data['pagination']['pages'][] = [
                'url' => $posts->url($page),
                'number' => $page,
                'active' => $page == $posts->currentPage()
            ];
        }

        $breadcrumbs = [
            'items' => [
                [
                    'name' => 'Trang chủ',
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => 'Tìm kiếm',
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => 'Tìm kiếm'
        ];

        return view($this->getViewPath('post.search'), compact('data', 'breadcrumbs') + $seoData);
    }

    /**
     * Hiển thị trang tin tức chính
     *
     * @return \Illuminate\View\View
     */
    public function newsIndex()
    {
        // Breadcrumbs cho trang tin tức
        $breadcrumbs = [
            'items' => [
                [
                    'name' => 'Trang chủ',
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => 'Tin tức',
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => 'Tin tức'
        ];

        // Lấy bài viết nổi bật (featured post) - loại trừ dự án
        $featuredPost = Post::where('status', 'published')
            ->where('is_featured', true)
            ->whereDoesntHave('categories', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->first();

        // Lấy các bài viết thường (không phải featured) - loại trừ dự án
        $regularPosts = Post::where('status', 'published')
            ->where('id', '!=', $featuredPost ? $featuredPost->id : 0)
            ->whereDoesntHave('categories', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->limit(4)
            ->get();

        // Lấy bài viết mới nhất cho sidebar - loại trừ dự án
        $recentPosts = Post::where('status', 'published')
            ->whereDoesntHave('categories', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();

        // Lấy danh mục bài viết (không bao gồm dự án)
        $allPostCategories = Category::where('type', 'post')
            ->where('status', 'active')
            ->where('slug', '!=', 'du-an')
            ->where('lang', app()->getLocale())
            ->orderBy('name')
            ->get();

        $postCategories = $allPostCategories->take(5);
        $hasMoreCategories = $allPostCategories->count() > 5;

        $data = [
            'featuredPost' => $featuredPost,
            'regularPosts' => $regularPosts,
            'recentPosts' => $recentPosts,
            'postCategories' => $postCategories,
            'allPostCategories' => $allPostCategories,
            'hasMoreCategories' => $hasMoreCategories
        ];

        return view('templates.auvista.pages.news', compact('data', 'breadcrumbs'));
    }

    /**
     * Hiển thị trang dự án chính (tất cả dự án)
     *
     * @return \Illuminate\View\View
     */
    public function projectIndex()
    {
        // Breadcrumbs cho trang dự án
        $breadcrumbs = [
            'items' => [
                [
                    'name' => 'Trang chủ',
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => 'Dự án',
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => 'Dự án'
        ];

        // Lấy tất cả dự án
        $projects = Post::where('status', 'published')
            ->whereHas('categories', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        // Lấy các lĩnh vực dự án (category con của "Dự án")
        $projectCategories = Category::where('type', 'post')
            ->where('status', 'active')
            ->whereHas('parent', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->orderBy('name')
            ->get();

        // Lấy featured projects cho sidebar
        $featuredProjects = Post::where('status', 'published')
            ->where('is_featured', true)
            ->whereHas('categories', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();

        $data = [
            'projects' => $projects,
            'projectCategories' => $projectCategories,
            'featuredProjects' => $featuredProjects,
            'currentCategory' => null
        ];

        return view('templates.auvista.pages.projects', compact('data', 'breadcrumbs'));
    }

    /**
     * Hiển thị dự án theo lĩnh vực
     *
     * @param string $categorySlug
     * @return \Illuminate\View\View
     */
    public function projectsByCategory($categorySlug)
    {
        // Tìm category theo slug
        $category = Category::where('slug', $categorySlug)
            ->where('lang', app()->getLocale())
            ->select('id', 'name', 'description')
            ->firstOrFail();

        if (!$category) {
            abort(404);
        }

        // Breadcrumbs cho trang dự án theo lĩnh vực
        $breadcrumbs = [
            'items' => [
                [
                    'name' => 'Trang chủ',
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => 'Dự án',
                    'url' => route('du-an'),
                    'active' => false
                ],
                [
                    'name' => $category->name,
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => $category->name
        ];

        // Lấy dự án theo lĩnh vực
        $projects = Post::where('status', 'published')
            ->whereHas('categories', function ($query) use ($categorySlug) {
                $query->where('slug', $categorySlug);
            })
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->paginate(12);

        // Lấy các lĩnh vực dự án (category con của "Dự án")
        $projectCategories = Category::where('type', 'post')
            ->where('status', 'active')
            ->whereHas('parent', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->orderBy('name')
            ->get();

        // Lấy featured projects cho sidebar
        $featuredProjects = Post::where('status', 'published')
            ->where('is_featured', true)
            ->whereHas('categories', function ($query) {
                $query->where('slug', 'du-an');
            })
            ->where('lang', app()->getLocale())
            ->with(['categories' => function ($query) {
                $query->where('type', 'post');
            }])
            ->orderBy('published_at', 'desc')
            ->limit(5)
            ->get();

        $data = [
            'projects' => $projects,
            'projectCategories' => $projectCategories,
            'featuredProjects' => $featuredProjects,
            'currentCategory' => $category
        ];

        return view('templates.auvista.pages.projects', compact('data', 'breadcrumbs'));
    }
}
