// Import CSS
import 'bootstrap/dist/css/bootstrap.min.css';
import 'sweetalert2/dist/sweetalert2.min.css';
import '../assets/css/swiper-bundle.min.css';
import '../assets/css/style.css';

// Import jQuery
import $ from 'jquery';
window.$ = window.jQuery = $;

// Import Bootstrap with all components
import * as bootstrap from 'bootstrap';
window.bootstrap = bootstrap;

// Import Swiper
import Swiper from 'swiper/bundle';
window.Swiper = Swiper;

// Import SweetAlert2
import Swal from 'sweetalert2';
window.Swal = Swal;

// Import Livewire - Use the standard approach
import './bootstrap';

// Import main.js module
import '../assets/js/main.js';

// Import './bootstrap';
// Import './cart';

// Set global flag to prevent other mobile menu scripts from running
// window.MOBILE_MENU_HANDLED = true; // We will let main.js handle this

// Initialize Swiper instances
// This is already handled by initializeSwiperSliders() in main.js
// function initSwiper() { ... }

// Initialize Bootstrap components
function initBootstrap() {
    // Initialize all tooltips
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // Initialize all popovers
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function (popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });

    // Initialize dropdowns
    const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    dropdownElementList.map(function (dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Initialize modals
    const modalElementList = [].slice.call(document.querySelectorAll('.modal'));
    modalElementList.map(function (modalEl) {
        return new bootstrap.Modal(modalEl);
    });
}

// Remove conflicting mobile menu functions, as main.js will handle it.
// window.toggleMobileMenu = function() { ... };
// window.closeMobileMenuOnOutsideClick = function(event) { ... };

// Initialize Back to Top button
// This is also handled in main.js if it exists there, but we can keep it as a fallback.
function initBackToTop() {
    const backToTop = document.getElementById('back-to-top');
    if (!backToTop) return;

    const toggleBackToTop = () => {
        if (window.scrollY > 300) {
            backToTop.classList.add('show');
        } else {
            backToTop.classList.remove('show');
        }
    };

    window.addEventListener('scroll', toggleBackToTop);
    
    backToTop.addEventListener('click', (e) => {
        e.preventDefault();
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// Initialize everything when DOM is ready
$(document).ready(function() {
    // initSwiper(); // Let main.js handle it
    initBackToTop();
    
    // Initialize Bootstrap with a small delay to ensure DOM is fully ready
    setTimeout(() => {
        initBootstrap();
        
        // Add document click listener for closing menu
        document.addEventListener('click', window.closeMobileMenuOnOutsideClick);
        
        // Add ESC key listener
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                const navbarCollapse = document.querySelector('#mainNav');
                if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                    window.toggleMobileMenu();
                }
            }
        });
        
        // Close menu on window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth > 991) {
                const navbarCollapse = document.querySelector('#mainNav');
                if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                    window.toggleMobileMenu();
                }
            }
        });
        
        // Close menu when clicking nav links (mobile)
        document.addEventListener('click', function(e) {
            const navLink = e.target.closest('.nav-link');
            if (navLink && window.innerWidth <= 991) {
                const navbarCollapse = document.querySelector('#mainNav');
                if (navbarCollapse && navbarCollapse.classList.contains('show')) {
                    setTimeout(() => {
                        window.toggleMobileMenu();
                    }, 100);
                }
            }
        });
        
    }, 50);
});

// Also initialize on DOMContentLoaded as a fallback
document.addEventListener('DOMContentLoaded', function() {
    // Only run if jQuery version hasn't run yet
    setTimeout(() => {
        initBootstrap();
    }, 100);
});

// Xử lý sự kiện Livewire cho cart
document.addEventListener('livewire:initialized', () => {
    // Lắng nghe sự kiện khi giỏ hàng được cập nhật
    Livewire.on('cart-updated', (event) => {
        console.log('Cart updated event received:', event); // Debug log
        
        // Xử lý cả trường hợp event là object hoặc array
        let eventData = event;
        if (Array.isArray(event) && event.length > 0) {
            eventData = event[0]; // Lấy phần tử đầu tiên nếu là array
        }
        
        console.log('Processed event data:', eventData); // Debug log
        
        // Cập nhật số lượng sản phẩm trong giỏ hàng
        const cartCountElements = document.querySelectorAll('.cart-count');
        cartCountElements.forEach(el => {
            el.textContent = eventData.cartCount;
            // Thêm hiệu ứng nhấp nháy để người dùng chú ý
            el.classList.add('text-white', 'bg-danger');
            setTimeout(() => {
                el.classList.remove('text-white', 'bg-danger');
            }, 1000);
        });

        // Hiển thị thông báo dựa trên message
        if (eventData.message) {
            console.log('Showing custom message:', eventData.message); // Debug log
            Swal.fire({
                title: 'Thành công!',
                text: eventData.message,
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        } else {
            console.log('No custom message, using default'); // Debug log
            // Chỉ hiển thị thông báo thêm sản phẩm cho những event không có message cụ thể
            Swal.fire({
                title: 'Thành công!',
                text: 'Sản phẩm đã được thêm vào giỏ hàng',
                icon: 'success',
                timer: 2000,
                showConfirmButton: false
            });
        }
    });

    // Lắng nghe sự kiện khi có lỗi
    Livewire.on('error', (event) => {
        Swal.fire({
            title: 'Lỗi!',
            text: event.message,
            icon: 'error'
        });
    });

    // Re-initialize Bootstrap components after Livewire updates
    Livewire.hook('morph.updated', ({ el, component }) => {
        // Re-initialize Bootstrap modals specifically for cart popup
        if (component.name === 'cart-popup') {
            setTimeout(() => {
                const cartModal = document.getElementById('cartModal');
                if (cartModal && !bootstrap.Modal.getInstance(cartModal)) {
                    new bootstrap.Modal(cartModal);
                }
            }, 100);
        }
    });
});

// Manual cart modal handler as fallback
document.addEventListener('click', function(e) {
    // Check if clicked element has cart modal trigger attributes
    const cartTrigger = e.target.closest('[data-bs-toggle="modal"][data-bs-target="#cartModal"]');
    if (cartTrigger) {
        e.preventDefault();
        
        // Find the cart modal
        const cartModal = document.getElementById('cartModal');
        if (cartModal) {
            // Try to get existing Bootstrap modal instance
            let modalInstance = bootstrap.Modal.getInstance(cartModal);
            
            // If no instance exists, create a new one
            if (!modalInstance) {
                modalInstance = new bootstrap.Modal(cartModal);
            }
            
            // Show the modal
            modalInstance.show();
        }
    }
});

// Xử lý sự kiện cho nút thêm vào giỏ hàng
document.addEventListener('click', function(e) {
    if (e.target.closest('.add-to-cart-btn')) {
        const button = e.target.closest('.add-to-cart-btn');
        const productId = button.dataset.productId;
        const quantity = button.dataset.quantity || 1;
        
        // Sử dụng fallback cart route
        fetch('/cart/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
            },
            body: JSON.stringify({
                product_id: productId,
                quantity: quantity
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // Dispatch Livewire event để cập nhật UI
                if (window.Livewire) {
                    Livewire.dispatch('cart-updated', {
                        cartCount: data.cartCount,
                        cartItems: {},
                        message: 'Sản phẩm đã được thêm vào giỏ hàng' // Thêm message cụ thể
                    });
                }
                
                // Không cần SweetAlert ở đây vì sẽ được xử lý bởi Livewire event listener
            } else {
                if (window.Swal) {
                    Swal.fire({
                        title: 'Lỗi!',
                        text: data.message,
                        icon: 'error'
                    });
                } else {
                    alert('Có lỗi xảy ra: ' + data.message);
                }
            }
        })
        .catch(error => {
            console.error('Error:', error);
            if (window.Swal) {
                Swal.fire({
                    title: 'Lỗi!',
                    text: 'Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng',
                    icon: 'error'
                });
            } else {
                alert('Có lỗi xảy ra khi thêm sản phẩm vào giỏ hàng');
            }
        });
    }
});
