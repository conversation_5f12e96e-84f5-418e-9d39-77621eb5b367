@extends('templates.auvista.layouts.default')

@section('title', 'Test Category Page')

@section('content')
<div class="container mx-auto py-8">
    <h1 class="text-3xl font-bold mb-4">Test Category Page</h1>
    
    @if(isset($category))
        <div class="bg-green-100 p-4 rounded mb-4">
            <h2 class="text-xl font-semibold">Category Found:</h2>
            <p><strong>Name:</strong> {{ $category->name }}</p>
            <p><strong>Slug:</strong> {{ $category->slug }}</p>
            <p><strong>Type:</strong> {{ $category->type }}</p>
            <p><strong>Status:</strong> {{ $category->status }}</p>
        </div>
    @else
        <div class="bg-red-100 p-4 rounded mb-4">
            <p>No category data found</p>
        </div>
    @endif
    
    @if(isset($products))
        <div class="bg-blue-100 p-4 rounded mb-4">
            <h2 class="text-xl font-semibold">Products Found: {{ $products->count() }}</h2>
            @foreach($products as $product)
                <div class="border-b py-2">
                    <p><strong>{{ $product->name }}</strong></p>
                    <p>Price: {{ number_format($product->price) }}đ</p>
                </div>
            @endforeach
        </div>
    @else
        <div class="bg-yellow-100 p-4 rounded mb-4">
            <p>No products data found</p>
        </div>
    @endif
    
    <div class="mt-4">
        <a href="{{ route('products.index') }}" class="bg-blue-500 text-white px-4 py-2 rounded">
            Back to Products
        </a>
    </div>
</div>
@endsection
