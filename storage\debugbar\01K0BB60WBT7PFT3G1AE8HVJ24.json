{"__meta": {"id": "01K0BB60WBT7PFT3G1AE8HVJ24", "datetime": "2025-07-17 11:44:47", "utime": **********.372259, "method": "POST", "uri": "/livewire/update", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752727476.392419, "end": **********.372319, "duration": 10.979899883270264, "duration_str": "10.98s", "measures": [{"label": "Booting", "start": 1752727476.392419, "relative_start": 0, "end": **********.45125, "relative_end": **********.45125, "duration": 10.***************, "duration_str": "10.06s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.451275, "relative_start": 10.***************, "end": **********.372324, "relative_end": 5.0067901611328125e-06, "duration": 0.****************, "duration_str": "921ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.667939, "relative_start": 10.***************, "end": **********.683537, "relative_end": **********.683537, "duration": 0.015598058700561523, "duration_str": "15.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament::components.section.index", "start": **********.35182, "relative_start": 10.**************, "end": **********.35182, "relative_end": **********.35182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.section.heading", "start": **********.356418, "relative_start": 10.***************, "end": **********.356418, "relative_end": **********.356418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": **********.359606, "relative_start": 10.********779541, "end": **********.359606, "relative_end": **********.359606, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.364526, "relative_start": 10.97210693359375, "end": **********.36753, "relative_end": **********.36753, "duration": 0.0030040740966796875, "duration_str": "3ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 41292240, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "filament::components.section.index", "param_count": null, "params": [], "start": **********.351772, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/index.blade.phpfilament::components.section.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.section.heading", "param_count": null, "params": [], "start": **********.356366, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/section/heading.blade.phpfilament::components.section.heading", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fsection%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}}, {"name": "filament::components.grid.column", "param_count": null, "params": [], "start": **********.359569, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}}]}, "queries": {"count": 37, "nb_statements": 37, "nb_visible_statements": 37, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.08488, "accumulated_duration_str": "84.88ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1482718, "duration": 0.005690000000000001, "duration_str": "5.69ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 6.704}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.183373, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 6.704, "width_percent": 3.735}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1911829, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 10.438, "width_percent": 1.909}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-06'", "type": "query", "params": [], "bindings": ["2025-07-06"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.1965098, "duration": 0.0028799999999999997, "duration_str": "2.88ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 12.347, "width_percent": 3.393}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.201527, "duration": 0.0029100000000000003, "duration_str": "2.91ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 15.74, "width_percent": 3.428}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.206009, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 19.168, "width_percent": 1.744}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-07'", "type": "query", "params": [], "bindings": ["2025-07-07"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2089689, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 20.912, "width_percent": 2.769}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.213189, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 23.68, "width_percent": 1.473}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.215934, "duration": 0.00209, "duration_str": "2.09ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 25.153, "width_percent": 2.462}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-08'", "type": "query", "params": [], "bindings": ["2025-07-08"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.219489, "duration": 0.0016699999999999998, "duration_str": "1.67ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 27.615, "width_percent": 1.967}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.223129, "duration": 0.0025099999999999996, "duration_str": "2.51ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 29.583, "width_percent": 2.957}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.227262, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 32.54, "width_percent": 1.85}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-09'", "type": "query", "params": [], "bindings": ["2025-07-09"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.231133, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 34.39, "width_percent": 2.698}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.235067, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 37.088, "width_percent": 1.532}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.23843, "duration": 0.00231, "duration_str": "2.31ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 38.619, "width_percent": 2.721}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-10'", "type": "query", "params": [], "bindings": ["2025-07-10"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2422822, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 41.341, "width_percent": 2.439}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.246265, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 43.779, "width_percent": 1.909}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2491999, "duration": 0.00164, "duration_str": "1.64ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 45.688, "width_percent": 1.932}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-11'", "type": "query", "params": [], "bindings": ["2025-07-11"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.252607, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 47.62, "width_percent": 1.779}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.255644, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 49.399, "width_percent": 1.355}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.258994, "duration": 0.00242, "duration_str": "2.42ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 50.754, "width_percent": 2.851}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-12'", "type": "query", "params": [], "bindings": ["2025-07-12"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.262787, "duration": 0.0022299999999999998, "duration_str": "2.23ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 53.605, "width_percent": 2.627}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.267253, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 56.232, "width_percent": 2.25}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.271064, "duration": 0.0030800000000000003, "duration_str": "3.08ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 58.483, "width_percent": 3.629}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-13'", "type": "query", "params": [], "bindings": ["2025-07-13"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.276229, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 62.111, "width_percent": 2.297}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.280368, "duration": 0.00156, "duration_str": "1.56ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 64.409, "width_percent": 1.838}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.283357, "duration": 0.0020099999999999996, "duration_str": "2.01ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 66.246, "width_percent": 2.368}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-14'", "type": "query", "params": [], "bindings": ["2025-07-14"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.28734, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 68.615, "width_percent": 2.863}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.291756, "duration": 0.00321, "duration_str": "3.21ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 71.477, "width_percent": 3.782}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.296651, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 75.259, "width_percent": 1.661}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-15'", "type": "query", "params": [], "bindings": ["2025-07-15"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.2998662, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 76.92, "width_percent": 3.829}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3050709, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 80.749, "width_percent": 2.91}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.309344, "duration": 0.0025499999999999997, "duration_str": "2.55ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 83.659, "width_percent": 3.004}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-16'", "type": "query", "params": [], "bindings": ["2025-07-16"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.3138208, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 86.664, "width_percent": 2.969}, {"sql": "select count(*) as aggregate from `orders` where date(`created_at`) = '2025-07-17'", "type": "query", "params": [], "bindings": ["2025-07-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.318422, "duration": 0.00229, "duration_str": "2.29ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:30", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=30", "ajax": false, "filename": "TreatmentsChart.php", "line": "30"}, "connection": "auvista", "explain": null, "start_percent": 89.632, "width_percent": 2.698}, {"sql": "select count(*) as aggregate from `posts` where date(`created_at`) = '2025-07-17'", "type": "query", "params": [], "bindings": ["2025-07-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.322775, "duration": 0.00289, "duration_str": "2.89ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:33", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 33}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=33", "ajax": false, "filename": "TreatmentsChart.php", "line": "33"}, "connection": "auvista", "explain": null, "start_percent": 92.33, "width_percent": 3.405}, {"sql": "select count(*) as aggregate from `products` where date(`created_at`) = '2025-07-17'", "type": "query", "params": [], "bindings": ["2025-07-17"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, {"index": 17, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 58}, {"index": 18, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 50}, {"index": 19, "namespace": null, "name": "vendor/filament/widgets/src/ChartWidget.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\widgets\\src\\ChartWidget.php", "line": 102}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.32821, "duration": 0.00362, "duration_str": "3.62ms", "memory": 0, "memory_str": null, "filename": "TreatmentsChart.php:36", "source": {"index": 16, "namespace": null, "name": "app/Filament/Widgets/TreatmentsChart.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Widgets\\TreatmentsChart.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FWidgets%2FTreatmentsChart.php&line=36", "ajax": false, "filename": "TreatmentsChart.php", "line": "36"}, "connection": "auvista", "explain": null, "start_percent": 95.735, "width_percent": 4.265}]}, "models": {"data": {"App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": {"app.filament.widgets.treatments-chart #hLKXlKvwoQugUywV0hLT": "array:4 [\n  \"data\" => array:2 [\n    \"dataChecksum\" => \"d487b41032c70ca2163627b33ab14c8b\"\n    \"filter\" => null\n  ]\n  \"name\" => \"app.filament.widgets.treatments-chart\"\n  \"component\" => \"App\\Filament\\Widgets\\TreatmentsChart\"\n  \"id\" => \"hLKXlKvwoQugUywV0hLT\"\n]"}, "count": 1}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate", "uri": "POST livewire/update", "controller": "App\\Filament\\Widgets\\TreatmentsChart@updateChartData<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Fwidgets%2Fsrc%2FChartWidget.php&line=100\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/widgets/src/ChartWidget.php:100-109</a>", "middleware": "web", "duration": "10.87s", "peak_memory": "44MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1392152045 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1392152045\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1278903677 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>components</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>snapshot</span>\" => \"<span class=sf-dump-str title=\"331 characters\">{&quot;data&quot;:{&quot;dataChecksum&quot;:&quot;d487b41032c70ca2163627b33ab14c8b&quot;,&quot;filter&quot;:null},&quot;memo&quot;:{&quot;id&quot;:&quot;hLKXlKvwoQugUywV0hLT&quot;,&quot;name&quot;:&quot;app.filament.widgets.treatments-chart&quot;,&quot;path&quot;:&quot;admin&quot;,&quot;method&quot;:&quot;GET&quot;,&quot;children&quot;:[],&quot;scripts&quot;:[],&quot;assets&quot;:[],&quot;errors&quot;:[],&quot;locale&quot;:&quot;vi&quot;},&quot;checksum&quot;:&quot;1fb4cb7031762f738bf6e4f51ea9338becbc4bb11ad84afadd4930f7c17186cf&quot;}</span>\"\n      \"<span class=sf-dump-key>updates</span>\" => []\n      \"<span class=sf-dump-key>calls</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>path</span>\" => \"\"\n          \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"15 characters\">updateChartData</span>\"\n          \"<span class=sf-dump-key>params</span>\" => []\n        </samp>]\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1278903677\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1594777248 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">531</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"20 characters\">https://auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">https://auvista.test/admin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6ImkyWldjQ21VTnRFZU02MzdIU0FoVkE9PSIsInZhbHVlIjoiZ09rT20vanlmYkNaSUtHR3hrNmhLdVJJTHJOVVZZdkJqZ0xjTDhLekd2dFp5UWhMZ0lJa08rak90NnIxV2VZNGxPcm94a25aMVdxcWhjYmxNVG5xbk1EVjZGWkR4MldTcHVVTyswcGxNNVM5Nk04UGNOOEs2d1RPU0oyaVFvTHYiLCJtYWMiOiJkMGU1Y2VlZDUwNGQzODQ5NTQ3ZmExY2E2OTdjYTI2MDk5MTQ4NTBlYWFjZWY2ZjI5Y2JkYzBiYTliYTZmMDE5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IlNMa25kcnc0YnkwWm84M0xOYWoxMnc9PSIsInZhbHVlIjoibnhQOWhJMTVLNjh5a2t1dVcreXNTR0xTV05KYXlDd3NrWXpxZWlGWUhzNkN2QkZYK2F0MGx6bTZHc0JoVWxUWGVpbWRsdk5xT2E5NFFMNFUvakdSUFRERm5iR3puWE1PR1JhWTZoMmtRYW9oNnJCazdRTVY5L3k2NUFGcXlkR2UiLCJtYWMiOiI3N2QzNDZhMjMzODIxNjZlOWY5OGZiNGNjYzAwODk1NjY5OTE0NDZlMjRlNTJhNDlmMWU0YWI0OGVkMTBlMzg2IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1594777248\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1022194768 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ALiu3MXg2qeWtslysnYzKUjGVbJ1TKvwt3KhqDy1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1022194768\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1464696918 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 04:44:47 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1464696918\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-69060170 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://auvista.test/admin/menus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$2uJGkMnwk7Lj203GgLThU.EI1Z7VNTd1oXu5zgFDqiWVi2sEnDYEy</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-69060170\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/livewire/update", "action_name": "livewire.update", "controller_action": "Livewire\\Mechanisms\\HandleRequests\\HandleRequests@handleUpdate"}, "badge": null}}