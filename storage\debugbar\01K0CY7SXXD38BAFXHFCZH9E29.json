{"__meta": {"id": "01K0CY7SXXD38BAFXHFCZH9E29", "datetime": "2025-07-18 02:37:03", "utime": **********.165958, "method": "GET", "uri": "/admin/login", "ip": "127.0.0.1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1752781004.896513, "end": **********.166011, "duration": 18.269498109817505, "duration_str": "18.27s", "measures": [{"label": "Booting", "start": 1752781004.896513, "relative_start": 0, "end": **********.216073, "relative_end": **********.216073, "duration": 0.*****************, "duration_str": "320ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.216082, "relative_start": 0.*****************, "end": **********.166016, "relative_end": 5.0067901611328125e-06, "duration": 17.***************, "duration_str": "17.95s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.344399, "relative_start": 0.*****************, "end": **********.346359, "relative_end": **********.346359, "duration": 0.0019600391387939453, "duration_str": "1.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament-panels::pages.auth.login", "start": **********.271497, "relative_start": 3.*************, "end": **********.271497, "relative_end": **********.271497, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.904438, "relative_start": 5.***************, "end": **********.904438, "relative_end": **********.904438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.977142, "relative_start": 5.**********36304, "end": **********.977142, "relative_end": **********.977142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1752781010.71568, "relative_start": 5.819166898727417, "end": 1752781010.71568, "relative_end": 1752781010.71568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": 1752781012.320881, "relative_start": 7.424367904663086, "end": 1752781012.320881, "relative_end": 1752781012.320881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": 1752781012.498926, "relative_start": 7.602412939071655, "end": 1752781012.498926, "relative_end": 1752781012.498926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": 1752781012.499972, "relative_start": 7.603459119796753, "end": 1752781012.499972, "relative_end": 1752781012.499972, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::b3fea7b79a291f72028b3ade68edbac3", "start": 1752781013.012465, "relative_start": 8.115952014923096, "end": 1752781013.012465, "relative_end": 1752781013.012465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752781013.354738, "relative_start": 8.458225011825562, "end": 1752781013.354738, "relative_end": 1752781013.354738, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752781014.903473, "relative_start": 10.006959915161133, "end": 1752781014.903473, "relative_end": 1752781014.903473, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::df5d8c26b1fd4ecd22aeee145299adc3", "start": 1752781015.099444, "relative_start": 10.202930927276611, "end": 1752781015.099444, "relative_end": 1752781015.099444, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": 1752781015.438636, "relative_start": 10.542123079299927, "end": 1752781015.438636, "relative_end": 1752781015.438636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": 1752781015.439829, "relative_start": 10.543316125869751, "end": 1752781015.439829, "relative_end": 1752781015.439829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::557f112bcfd40ff4ed71d8a0603209da", "start": 1752781015.463855, "relative_start": 10.567342042922974, "end": 1752781015.463855, "relative_end": 1752781015.463855, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": 1752781015.466011, "relative_start": 10.569498062133789, "end": 1752781015.466011, "relative_end": 1752781015.466011, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.checkbox", "start": 1752781016.014407, "relative_start": 11.117893934249878, "end": 1752781016.014407, "relative_end": 1752781016.014407, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::9b0aa906eb507785d5e713f2ff316d37", "start": 1752781016.1022, "relative_start": 11.205687046051025, "end": 1752781016.1022, "relative_end": 1752781016.1022, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.column", "start": 1752781016.284554, "relative_start": 11.388041019439697, "end": 1752781016.284554, "relative_end": 1752781016.284554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.grid.index", "start": 1752781016.285137, "relative_start": 11.3886239528656, "end": 1752781016.285137, "relative_end": 1752781016.285137, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.form.actions", "start": 1752781016.340271, "relative_start": 11.443758010864258, "end": 1752781016.340271, "relative_end": 1752781016.340271, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.actions", "start": 1752781016.510175, "relative_start": 11.613662004470825, "end": 1752781016.510175, "relative_end": 1752781016.510175, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": 1752781016.792881, "relative_start": 11.896368026733398, "end": 1752781016.792881, "relative_end": 1752781016.792881, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": 1752781016.987786, "relative_start": 12.091273069381714, "end": 1752781016.987786, "relative_end": 1752781016.987786, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752781018.605112, "relative_start": 13.708599090576172, "end": 1752781018.605112, "relative_end": 1752781018.605112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": 1752781018.65074, "relative_start": 13.754226922988892, "end": 1752781018.65074, "relative_end": 1752781018.65074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.form.index", "start": 1752781018.651108, "relative_start": 13.754595041275024, "end": 1752781018.651108, "relative_end": 1752781018.651108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.simple", "start": 1752781018.709197, "relative_start": 13.812684059143066, "end": 1752781018.709197, "relative_end": 1752781018.709197, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.header.simple", "start": 1752781019.154243, "relative_start": 14.25773000717163, "end": 1752781019.154243, "relative_end": 1752781019.154243, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.logo", "start": 1752781019.368752, "relative_start": 14.472239017486572, "end": 1752781019.368752, "relative_end": 1752781019.368752, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752781020.567478, "relative_start": 15.67096495628357, "end": 1752781020.567478, "relative_end": 1752781020.567478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752781021.358153, "relative_start": 16.461640119552612, "end": 1752781021.358153, "relative_end": 1752781021.358153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": 1752781021.359857, "relative_start": 16.46334409713745, "end": 1752781021.359857, "relative_end": 1752781021.359857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": 1752781021.372196, "relative_start": 16.475682973861694, "end": 1752781021.372196, "relative_end": 1752781021.372196, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.simple", "start": 1752781021.465399, "relative_start": 16.568886041641235, "end": 1752781021.465399, "relative_end": 1752781021.465399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.base", "start": 1752781022.048135, "relative_start": 17.15162205696106, "end": 1752781022.048135, "relative_end": 1752781022.048135, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": 1752781022.580072, "relative_start": 17.68355894088745, "end": 1752781022.580072, "relative_end": 1752781022.580072, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::27df4909b5b8fbf02f3a65ba2f758414", "start": 1752781022.717694, "relative_start": 17.821181058883667, "end": 1752781022.717694, "relative_end": 1752781022.717694, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.056385, "relative_start": 18.15987205505371, "end": **********.056385, "relative_end": **********.056385, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97c5322e947dbf60ae5ca480928dd519", "start": **********.058305, "relative_start": 18.161792039871216, "end": **********.058305, "relative_end": **********.058305, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.135229, "relative_start": 18.23871612548828, "end": **********.135381, "relative_end": **********.135381, "duration": 0.0001518726348876953, "duration_str": "152μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.161753, "relative_start": 18.26523995399475, "end": **********.161835, "relative_end": **********.161835, "duration": 8.20159912109375e-05, "duration_str": "82μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 43775080, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 39, "nb_templates": 39, "templates": [{"name": "filament-panels::pages.auth.login", "param_count": null, "params": [], "start": **********.271479, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/pages/auth/login.blade.phpfilament-panels::pages.auth.login", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fpages%2Fauth%2Flogin.blade.php&line=1", "ajax": false, "filename": "login.blade.php", "line": "?"}}, {"name": "filament::components.input.index", "param_count": null, "params": [], "start": **********.904407, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/index.blade.phpfilament::components.input.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.input.wrapper", "param_count": null, "params": [], "start": **********.977123, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/wrapper.blade.phpfilament::components.input.wrapper", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fwrapper.blade.php&line=1", "ajax": false, "filename": "wrapper.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": 1752781010.715657, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "filament::components.grid.column", "param_count": null, "params": [], "start": 1752781012.320861, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}}, {"name": "filament::components.input.index", "param_count": null, "params": [], "start": 1752781012.498895, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/index.blade.phpfilament::components.input.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.input.wrapper", "param_count": null, "params": [], "start": 1752781012.499872, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/wrapper.blade.phpfilament::components.input.wrapper", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fwrapper.blade.php&line=1", "ajax": false, "filename": "wrapper.blade.php", "line": "?"}}, {"name": "__components::b3fea7b79a291f72028b3ade68edbac3", "param_count": null, "params": [], "start": 1752781013.012427, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/b3fea7b79a291f72028b3ade68edbac3.blade.php__components::b3fea7b79a291f72028b3ade68edbac3", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Fb3fea7b79a291f72028b3ade68edbac3.blade.php&line=1", "ajax": false, "filename": "b3fea7b79a291f72028b3ade68edbac3.blade.php", "line": "?"}}, {"name": "filament::components.icon-button", "param_count": null, "params": [], "start": 1752781013.354719, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": 1752781014.903453, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "__components::df5d8c26b1fd4ecd22aeee145299adc3", "param_count": null, "params": [], "start": 1752781015.099408, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/df5d8c26b1fd4ecd22aeee145299adc3.blade.php__components::df5d8c26b1fd4ecd22aeee145299adc3", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Fdf5d8c26b1fd4ecd22aeee145299adc3.blade.php&line=1", "ajax": false, "filename": "df5d8c26b1fd4ecd22aeee145299adc3.blade.php", "line": "?"}}, {"name": "filament::components.icon-button", "param_count": null, "params": [], "start": 1752781015.438614, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}}, {"name": "filament::components.icon", "param_count": null, "params": [], "start": 1752781015.439814, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}}, {"name": "__components::557f112bcfd40ff4ed71d8a0603209da", "param_count": null, "params": [], "start": 1752781015.463811, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/557f112bcfd40ff4ed71d8a0603209da.blade.php__components::557f112bcfd40ff4ed71d8a0603209da", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F557f112bcfd40ff4ed71d8a0603209da.blade.php&line=1", "ajax": false, "filename": "557f112bcfd40ff4ed71d8a0603209da.blade.php", "line": "?"}}, {"name": "filament::components.grid.column", "param_count": null, "params": [], "start": 1752781015.465993, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}}, {"name": "filament::components.input.checkbox", "param_count": null, "params": [], "start": 1752781016.014385, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/checkbox.blade.phpfilament::components.input.checkbox", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fcheckbox.blade.php&line=1", "ajax": false, "filename": "checkbox.blade.php", "line": "?"}}, {"name": "__components::9b0aa906eb507785d5e713f2ff316d37", "param_count": null, "params": [], "start": 1752781016.102179, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/9b0aa906eb507785d5e713f2ff316d37.blade.php__components::9b0aa906eb507785d5e713f2ff316d37", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F9b0aa906eb507785d5e713f2ff316d37.blade.php&line=1", "ajax": false, "filename": "9b0aa906eb507785d5e713f2ff316d37.blade.php", "line": "?"}}, {"name": "filament::components.grid.column", "param_count": null, "params": [], "start": 1752781016.284532, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/column.blade.phpfilament::components.grid.column", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Fcolumn.blade.php&line=1", "ajax": false, "filename": "column.blade.php", "line": "?"}}, {"name": "filament::components.grid.index", "param_count": null, "params": [], "start": 1752781016.285122, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/grid/index.blade.phpfilament::components.grid.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fgrid%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament-panels::components.form.actions", "param_count": null, "params": [], "start": 1752781016.340249, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/form/actions.blade.phpfilament-panels::components.form.actions", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fform%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}}, {"name": "filament::components.actions", "param_count": null, "params": [], "start": 1752781016.510148, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/actions.blade.phpfilament::components.actions", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}}, {"name": "__components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": 1752781016.79285, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}}, {"name": "filament::components.button.index", "param_count": null, "params": [], "start": 1752781016.987758, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.loading-indicator", "param_count": null, "params": [], "start": 1752781018.605092, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}}, {"name": "filament::components.loading-indicator", "param_count": null, "params": [], "start": 1752781018.650723, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}}, {"name": "filament-panels::components.form.index", "param_count": null, "params": [], "start": 1752781018.651095, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/form/index.blade.phpfilament-panels::components.form.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fform%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament-panels::components.page.simple", "param_count": null, "params": [], "start": 1752781018.709174, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/page/simple.blade.phpfilament-panels::components.page.simple", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Fsimple.blade.php&line=1", "ajax": false, "filename": "simple.blade.php", "line": "?"}}, {"name": "filament-panels::components.header.simple", "param_count": null, "params": [], "start": 1752781019.154221, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/header/simple.blade.phpfilament-panels::components.header.simple", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fheader%2Fsimple.blade.php&line=1", "ajax": false, "filename": "simple.blade.php", "line": "?"}}, {"name": "filament-panels::components.logo", "param_count": null, "params": [], "start": 1752781019.368734, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/logo.blade.phpfilament-panels::components.logo", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}}, {"name": "filament::components.modal.index", "param_count": null, "params": [], "start": 1752781020.56746, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.modal.index", "param_count": null, "params": [], "start": 1752781021.358121, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "filament::components.modal.index", "param_count": null, "params": [], "start": 1752781021.359836, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}}, {"name": "__components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": 1752781021.372176, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}}, {"name": "filament-panels::components.layout.simple", "param_count": null, "params": [], "start": 1752781021.465372, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/layout/simple.blade.phpfilament-panels::components.layout.simple", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Fsimple.blade.php&line=1", "ajax": false, "filename": "simple.blade.php", "line": "?"}}, {"name": "filament-panels::components.layout.base", "param_count": null, "params": [], "start": 1752781022.048117, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/layout/base.blade.phpfilament-panels::components.layout.base", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}}, {"name": "filament::assets", "param_count": null, "params": [], "start": 1752781022.580054, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/assets.blade.phpfilament::assets", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fassets.blade.php&line=1", "ajax": false, "filename": "assets.blade.php", "line": "?"}}, {"name": "__components::27df4909b5b8fbf02f3a65ba2f758414", "param_count": null, "params": [], "start": 1752781022.717662, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/27df4909b5b8fbf02f3a65ba2f758414.blade.php__components::27df4909b5b8fbf02f3a65ba2f758414", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F27df4909b5b8fbf02f3a65ba2f758414.blade.php&line=1", "ajax": false, "filename": "27df4909b5b8fbf02f3a65ba2f758414.blade.php", "line": "?"}}, {"name": "filament::assets", "param_count": null, "params": [], "start": **********.056365, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/assets.blade.phpfilament::assets", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fassets.blade.php&line=1", "ajax": false, "filename": "assets.blade.php", "line": "?"}}, {"name": "__components::97c5322e947dbf60ae5ca480928dd519", "param_count": null, "params": [], "start": **********.058279, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/97c5322e947dbf60ae5ca480928dd519.blade.php__components::97c5322e947dbf60ae5ca480928dd519", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F97c5322e947dbf60ae5ca480928dd519.blade.php&line=1", "ajax": false, "filename": "97c5322e947dbf60ae5ca480928dd519.blade.php", "line": "?"}}]}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": {"app.filament.pages.auth.login-panel #T5UGgUFXpyklHQfcdkcr": "array:4 [\n  \"data\" => array:16 [\n    \"data\" => array:3 [\n      \"email\" => \"\"\n      \"password\" => \"\"\n      \"remember\" => false\n    ]\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n  ]\n  \"name\" => \"app.filament.pages.auth.login-panel\"\n  \"component\" => \"App\\Filament\\Pages\\Auth\\LoginPanel\"\n  \"id\" => \"T5UGgUFXpyklHQfcdkcr\"\n]", "filament.livewire.notifications #dYkQVr368KYaMmJ1Zmye": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#3133\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"dYkQVr368KYaMmJ1Zmye\"\n]"}, "count": 2}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/admin/login", "action_name": "filament.admin.auth.login", "controller_action": "App\\Filament\\Pages\\Auth\\LoginPanel", "uri": "GET admin/login", "controller": "App\\Filament\\Pages\\Auth\\LoginPanel@render<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/admin", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, App\\Http\\Middleware\\LogLogoutMiddleware, App\\Http\\Middleware\\LocaleMiddleware", "duration": "18.27s", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://auvista.test/admin/menus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkdKcGdJeHBGSDZRZ0ZGY2d6Q1VkN2c9PSIsInZhbHVlIjoiQlNTSjBGMHBYUTh4WXFmTjYyM0J6SVJpN002cy90OEJGbHlSV2JFdHJpZUlTaHRMZEZzenU0WUx4aWtvbTQ4alAxOWx0Q2hPU2hnR21vOFVFdWF6SitDMUM0Z3p5U1Z5ODFEb21sMnBsWVA2VnNGMFMyU1VlWjdlSkg0dThXeWEiLCJtYWMiOiJmZTM2MTA1MGNlNWRiMzFjMGY1YmRkMjIzOWQ3ZDQyNDdjYjY2ZTE2Y2EzMjY0M2ZkZWEwZjdlM2RmYWU0NTMyIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IllxcjM4ank2NncvZUZZTnRTL3NlMkE9PSIsInZhbHVlIjoia3ZpYXpncnpYUUQzTEJMNUxvQ2pGZDFybEh6QXNkK3JEWUpUeGlFZ1dsci9USHFEOUxJSCtrVXNNSVV1dmRTS2N5Z1hqbGdJMFN3Tm9ydXRpOXBCdG5SekJERmVlazliSDBUY2tBSEV0Q3hnZW9weEhKQ0o1dGQwOGpqZDF4QkYiLCJtYWMiOiIxYzI2NGZiNzllYmUyNDUxN2NlM2NkZjY5NDZkZjQxZjFmNTIxZjIyNTIyODNlZTYzODYyMzk1MDllMjBiODViIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-554500400 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8oY8Yr8s4g8DRTkwoiyA4kQpFvtwDn2e6tN5qeDw</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">G2ibNMV1nkw7Uhp8TXa0HO5Qk6Hzfiv4hn1lqr7m</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-554500400\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-353552253 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 19:37:03 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InNKS0JqYXRPRHpCNXFJRUR6OHV5N1E9PSIsInZhbHVlIjoiSHBqQVpqVGZwQTJldnNBNVZLR1RMK1RtSk1TY3p0cG1JMDU2eVdLeVY1R2tyWHBxbTZTdTlmVVkvN2t4MWk0OWxQa3k3ZENoQm9CWGpSYnB3NWNoNjRDclBMUFhNczFyUUhMR2NMOW1kT2MzR1VwV0dtdUFuOXdxVmhpMVhPNEIiLCJtYWMiOiI2YjZmOThiZmU3YTc4MjU4ZGM0MmEwYzQ0YzE1MGMxNzVjNzZlYTc2MDdiZjI1MWFhYTYzOWE4MWQ4OTQ0N2JiIiwidGFnIjoiIn0%3D; expires=Thu, 17 Jul 2025 21:37:03 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6IlhwbGFaenNBUWI2c0lLdGtIZ244a0E9PSIsInZhbHVlIjoiSGk4SGJkbEZMeG8xME56Rkh0T0VCL2NpUHdndWw5eDhqWnVYUmJERGJkS1FlVjU4T3E4NWFlaU4waEh3QWM2M0M3RnNyNENobldiTFlsNXFxcWZYRG1McnBrZmVUM2lNcXBBeFJhR3FNUG8zby9ya2FDYTNuTi8zODhmc2NoNnEiLCJtYWMiOiI2M2UwMGU0MGFmODI3NzVjNmIyYjdlNWM2ZWY0ZmMzYmJjNzM1MjgyNDU0ODk1NzE2NDIzZGQzNDY5NTU0MjIxIiwidGFnIjoiIn0%3D; expires=Thu, 17 Jul 2025 21:37:03 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InNKS0JqYXRPRHpCNXFJRUR6OHV5N1E9PSIsInZhbHVlIjoiSHBqQVpqVGZwQTJldnNBNVZLR1RMK1RtSk1TY3p0cG1JMDU2eVdLeVY1R2tyWHBxbTZTdTlmVVkvN2t4MWk0OWxQa3k3ZENoQm9CWGpSYnB3NWNoNjRDclBMUFhNczFyUUhMR2NMOW1kT2MzR1VwV0dtdUFuOXdxVmhpMVhPNEIiLCJtYWMiOiI2YjZmOThiZmU3YTc4MjU4ZGM0MmEwYzQ0YzE1MGMxNzVjNzZlYTc2MDdiZjI1MWFhYTYzOWE4MWQ4OTQ0N2JiIiwidGFnIjoiIn0%3D; expires=Thu, 17-Jul-2025 21:37:03 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6IlhwbGFaenNBUWI2c0lLdGtIZ244a0E9PSIsInZhbHVlIjoiSGk4SGJkbEZMeG8xME56Rkh0T0VCL2NpUHdndWw5eDhqWnVYUmJERGJkS1FlVjU4T3E4NWFlaU4waEh3QWM2M0M3RnNyNENobldiTFlsNXFxcWZYRG1McnBrZmVUM2lNcXBBeFJhR3FNUG8zby9ya2FDYTNuTi8zODhmc2NoNnEiLCJtYWMiOiI2M2UwMGU0MGFmODI3NzVjNmIyYjdlNWM2ZWY0ZmMzYmJjNzM1MjgyNDU0ODk1NzE2NDIzZGQzNDY5NTU0MjIxIiwidGFnIjoiIn0%3D; expires=Thu, 17-Jul-2025 21:37:03 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-353552253\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2095521875 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">8oY8Yr8s4g8DRTkwoiyA4kQpFvtwDn2e6tN5qeDw</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://auvista.test/admin/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>viewed_products</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-num>1</span>\n    <span class=sf-dump-index>1</span> => <span class=sf-dump-num>5</span>\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>5</span> => <span class=sf-dump-num>1</span>\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>intended</span>\" => \"<span class=sf-dump-str title=\"39 characters\">https://auvista.test/admin/menu-items/5</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2095521875\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/admin/login", "action_name": "filament.admin.auth.login", "controller_action": "App\\Filament\\Pages\\Auth\\LoginPanel"}, "badge": null}}