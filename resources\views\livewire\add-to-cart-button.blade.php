{{-- DEBUG: Component loaded with productId={{ $productId }}, showQuantity={{ $showQuantity ? 'true' : 'false' }} --}}
@if(!$showQuantity)
    <!-- Simple Add to <PERSON><PERSON> -->
    <button wire:click="addToCart" wire:loading.attr="disabled" title="Thêm vào giỏ hàng"
            class="w-11 h-11 bg-secondary-main text-white rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors shadow-lg">
        <div wire:loading.remove wire:target="addToCart">
            <svg width="23" height="22" viewBox="0 0 23 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path
                    d="M17.1738 16.2708H7.41133C6.50383 16.2708 5.633 15.8858 5.01883 15.2167C4.40467 14.5475 4.09301 13.6491 4.16634 12.7416L4.92718 3.61165C4.95468 3.32748 4.85383 3.05249 4.66133 2.84165C4.46883 2.63082 4.203 2.52081 3.91884 2.52081H2.33301C1.95717 2.52081 1.64551 2.20915 1.64551 1.83331C1.64551 1.45748 1.95717 1.14581 2.33301 1.14581H3.92801C4.59718 1.14581 5.22967 1.42998 5.67884 1.91581C5.92634 2.19081 6.10967 2.51165 6.2105 2.86915H17.6597C18.5855 2.86915 19.438 3.23581 20.0613 3.89581C20.6755 4.56498 20.9872 5.43582 20.9138 6.36165L20.4188 13.2366C20.318 14.9142 18.8513 16.2708 17.1738 16.2708ZM6.25634 4.23498L5.54134 12.8516C5.49551 13.3833 5.66967 13.8875 6.02717 14.2816C6.38467 14.6758 6.87967 14.8866 7.41133 14.8866H17.1738C18.1272 14.8866 18.9888 14.08 19.0622 13.1267L19.5572 6.25165C19.5938 5.71082 19.4197 5.19749 19.0622 4.82166C18.7047 4.43666 18.2097 4.2258 17.6688 4.2258H6.25634V4.23498Z"
                    fill="white" />
                <path
                    d="M15.3958 20.8542C14.3875 20.8542 13.5625 20.0292 13.5625 19.0208C13.5625 18.0125 14.3875 17.1875 15.3958 17.1875C16.4042 17.1875 17.2292 18.0125 17.2292 19.0208C17.2292 20.0292 16.4042 20.8542 15.3958 20.8542ZM15.3958 18.5625C15.1392 18.5625 14.9375 18.7642 14.9375 19.0208C14.9375 19.2775 15.1392 19.4792 15.3958 19.4792C15.6525 19.4792 15.8542 19.2775 15.8542 19.0208C15.8542 18.7642 15.6525 18.5625 15.3958 18.5625Z"
                    fill="white" />
                <path
                    d="M8.06283 20.8542C7.05449 20.8542 6.22949 20.0292 6.22949 19.0208C6.22949 18.0125 7.05449 17.1875 8.06283 17.1875C9.07116 17.1875 9.89616 18.0125 9.89616 19.0208C9.89616 20.0292 9.07116 20.8542 8.06283 20.8542ZM8.06283 18.5625C7.80616 18.5625 7.60449 18.7642 7.60449 19.0208C7.60449 19.2775 7.80616 19.4792 8.06283 19.4792C8.31949 19.4792 8.52116 19.2775 8.52116 19.0208C8.52116 18.7642 8.31949 18.5625 8.06283 18.5625Z"
                    fill="white" />
                <path
                    d="M19.75 8.02081H8.75C8.37417 8.02081 8.0625 7.70915 8.0625 7.33331C8.0625 6.95748 8.37417 6.64581 8.75 6.64581H19.75C20.1258 6.64581 20.4375 6.95748 20.4375 7.33331C20.4375 7.70915 20.1258 8.02081 19.75 8.02081Z"
                    fill="white" />
            </svg>
        </div>
        <div wire:loading wire:target="addToCart" class="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
    </button>
@else
<div class="space-y-5">
    <!-- Quantity Selector -->
    <div class="flex items-center gap-4">
        <span class="text-primary-base font-medium">Số lượng</span>
        <div class="flex items-center border border-gray-300 rounded-lg">
            <button
                class="w-10 h-10 flex items-center justify-center hover:bg-gray-100 transition-colors border-r"
                wire:click="decrement"
            >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4"/></svg>
            </button>
            <input
                type="number"
                wire:model.live="quantity"
                min="1"
                max="{{ $productStock }}"
                class="w-16 h-10 text-center border-0 focus:ring-0 focus:outline-none"
            />
            <button
                class="w-10 h-10 flex items-center justify-center hover:bg-gray-100 transition-colors border-l"
                wire:click="increment"
            >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"/></svg>
            </button>
        </div>
        <span class="text-primary-gray2">Còn {{ $productStock }} sản phẩm</span>
    </div>

    <!-- Action Buttons -->
    <div class="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 items-center gap-2 sm:gap-3 lg:gap-4">
        <!-- Store Location -->
        <a href="https://maps.google.com" target="_blank"
            class="col-span-1 h-10 sm:h-12 lg:h-[52px] border border-primary-green text-primary-green rounded-full font-medium hover:bg-green-50 transition-colors flex items-center justify-center gap-2 w-full"
        >
            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"/></svg>
            Chỉ đường đi
        </a>

        <!-- Quote Request -->
        <a href="/lien-he"
            class="col-span-1 h-10 sm:h-12 lg:h-[52px] border border-primary-price text-primary-price rounded-full font-medium hover:bg-red-50 transition-colors w-full flex items-center justify-center"
        >
            Tư vấn & báo giá
        </a>

        <!-- Add to Cart -->
        <button
            class="add-to-cart-btn col-span-2 lg:col-span-1 h-10 sm:h-12 lg:h-[52px] border border-secondary-main text-secondary-main rounded-full font-medium hover:bg-blue-200 transition-colors flex items-center justify-center gap-2 w-full whitespace-nowrap"
            data-product-id="{{ $productId }}"
            data-quantity="{{ $quantity }}"
        >
            <div wire:loading.remove wire:target="addToCart">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M18.19 17.75H7.53999C6.54999 17.75 5.59999 17.33 4.92999 16.6C4.25999 15.87 3.92 14.89 4 13.9L4.83 3.94C4.86 3.63 4.74999 3.33001 4.53999 3.10001C4.32999 2.87001 4.04 2.75 3.73 2.75H2C1.59 2.75 1.25 2.41 1.25 2C1.25 1.59 1.59 1.25 2 1.25H3.74001C4.47001 1.25 5.15999 1.56 5.64999 2.09C5.91999 2.39 6.12 2.74 6.23 3.13H18.72C19.73 3.13 20.66 3.53 21.34 4.25C22.01 4.98 22.35 5.93 22.27 6.94L21.73 14.44C21.62 16.27 20.02 17.75 18.19 17.75ZM6.28 4.62L5.5 14.02C5.45 14.6 5.64 15.15 6.03 15.58C6.42 16.01 6.95999 16.24 7.53999 16.24H18.19C19.23 16.24 20.17 15.36 20.25 14.32L20.79 6.82001C20.83 6.23001 20.64 5.67001 20.25 5.26001C19.86 4.84001 19.32 4.60999 18.73 4.60999H6.28V4.62Z" fill="#246DDA"/><path d="M16.25 22.75C15.15 22.75 14.25 21.85 14.25 20.75C14.25 19.65 15.15 18.75 16.25 18.75C17.35 18.75 18.25 19.65 18.25 20.75C18.25 21.85 17.35 22.75 16.25 22.75ZM16.25 20.25C15.97 20.25 15.75 20.47 15.75 20.75C15.75 21.03 15.97 21.25 16.25 21.25C16.53 21.25 16.75 21.03 16.75 20.75C16.75 20.47 16.53 20.25 16.25 20.25Z" fill="#246DDA"/><path d="M8.25 22.75C7.15 22.75 6.25 21.85 6.25 20.75C6.25 19.65 7.15 18.75 8.25 18.75C9.35 18.75 10.25 19.65 10.25 20.75C10.25 21.85 9.35 22.75 8.25 22.75ZM8.25 20.25C7.97 20.25 7.75 20.47 7.75 20.75C7.75 21.03 7.97 21.25 8.25 21.25C8.53 21.25 8.75 21.03 8.75 20.75C8.75 20.47 8.53 20.25 8.25 20.25Z" fill="#246DDA"/><path d="M21 8.75H9C8.59 8.75 8.25 8.41 8.25 8C8.25 7.59 8.59 7.25 9 7.25H21C21.41 7.25 21.75 7.59 21.75 8C21.75 8.41 21.41 8.75 21 8.75Z" fill="#246DDA"/></svg>
                Thêm vào giỏ hàng
            </div>
            <div wire:loading wire:target="addToCart">
                Đang thêm...
            </div>
        </button>

        <!-- Buy Now -->
        <button
            class="buy-now-btn col-span-2 lg:col-span-3 h-10 sm:h-12 lg:h-[52px] bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-full font-medium hover:from-blue-600 hover:to-blue-700 transition-all duration-300 w-full"
            data-product-id="{{ $productId }}"
            data-quantity="{{ $quantity }}"
        >
            Mua ngay
        </button>
    </div>
</div>
@endif 