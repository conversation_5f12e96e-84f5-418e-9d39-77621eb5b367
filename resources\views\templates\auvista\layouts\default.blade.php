<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    {{-- SEO Meta Tags --}}
    <meta name="description"
        content="{{ isset($seo_description) ? $seo_description : getSetting('site_description', '') }}">
    <meta name="keywords" content="{{ isset($seo_keywords) ? $seo_keywords : getSetting('site_keywords', '') }}">
    <meta name="author" content="{{ isset($seo_author) ? $seo_author : getSetting('site_name', '') }}">
    <meta name="robots" content="noindex, nofollow">
    <meta name="canonical" content="{{ isset($seo_canonical) ? $seo_canonical : url()->current() }}">

    {{-- Open Graph Tags for Social Sharing --}}
    <meta property="og:title" content="{{ isset($seo_title) ? $seo_title : getSetting('site_title', '') }}">
    <meta property="og:description"
        content="{{ isset($seo_description) ? $seo_description : getSetting('site_description', '') }}">
    <meta property="og:image" content="{{ isset($seo_image) ? $seo_image : getSetting('og_image', '') }}">
    <meta property="og:url" content="{{ isset($seo_canonical) ? $seo_canonical : url()->current() }}">
    <meta property="og:type" content="{{ isset($seo_type) ? $seo_type : 'website' }}">
    <meta property="og:site_name" content="{{ isset($seo_author) ? $seo_author : getSetting('site_name', '') }}">

    {{-- Twitter Card Tags --}}
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="{{ isset($seo_title) ? $seo_title : getSetting('site_title', '') }}">
    <meta name="twitter:description"
        content="{{ isset($seo_description) ? $seo_description : getSetting('site_description', '') }}">
    <meta name="twitter:image" content="{{ isset($seo_image) ? $seo_image : getSetting('og_image', '') }}">


    {{-- CSRF Token --}}
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>{{ isset($seo_title) ? $seo_title : getSetting('site_title', '') }}</title>

    {{-- Favicon --}}
    @php
        $faviconPath = getSetting('site_logo'); // Ưu tiên site_logo trước
        if (empty($faviconPath)) {
            $faviconPath = getSetting('site_favicon'); // Dự phòng site_favicon
        }
        if (empty($faviconPath)) {
            $faviconPath = 'favicon/favicon.ico';
        }

        // Tạo URL favicon đơn giản
        if (filter_var($faviconPath, FILTER_VALIDATE_URL)) {
            $faviconUrl = $faviconPath;
        } elseif (file_exists(storage_path('app/public/' . $faviconPath))) {
            $faviconUrl = asset('storage/' . $faviconPath);
        } else {
            $faviconUrl = asset($faviconPath);
        }
    @endphp

    <link rel="icon" href="{{ $faviconUrl }}" type="image/x-icon">
    <link rel="shortcut icon" href="{{ $faviconUrl }}" type="image/x-icon">
    <link rel="apple-touch-icon" href="{{ $faviconUrl }}">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ $faviconUrl }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ $faviconUrl }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ $faviconUrl }}">

    {{-- Font Awesome --}}
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" />

    {{-- Swiper CSS --}}
    <link rel="stylesheet" href="{{ asset('assets/css/swiper-bundle.min.css') }}" />

    {{-- Vite will now handle all CSS and JS through this single entry point --}}
    @vite('resources/js/app.js')

    @stack('styles')
    @livewireStyles

    {{-- Include Product Styles (except for categories page) --}}
    @unless (request()->routeIs('products.categories'))
        @include('templates.auvista.components.product_styles')
    @endunless

    <style>
        /* Force HTML template styles to override any conflicting styles */
        .bg-gradient8 {
            background: linear-gradient(180deg, #04389D 0%, #17C1F5 100%) !important;
            background-image: linear-gradient(180deg, #04389D 0%, #17C1F5 100%) !important;
        }

        /* Ensure rounded corners work */
        .rounded-3xl {
            border-radius: 1.5rem !important;
        }

        /* Ensure wave image displays */
        .relative.z-10 {
            position: relative !important;
            z-index: 10 !important;
        }

        /* Error Pages */
        .error-page {
            min-height: 60vh;
            display: flex;
            align-items: center;
        }

        .error-content {
            padding: 2rem;
        }

        .error-code {
            font-size: 8rem;
            font-weight: 700;
            color: #e5e7eb;
            line-height: 1;
            margin-bottom: 1rem;
        }

        .error-title {
            font-size: 2rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 1rem;
        }

        .error-message {
            font-size: 1.125rem;
            color: #6b7280;
            margin-bottom: 2rem;
        }

        .error-page .btn-primary {
            padding: 0.75rem 2rem;
            font-size: 1.125rem;
            font-weight: 500;
            border-radius: 0.5rem;
            transition: all 0.3s ease;
        }

        .error-page .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* Remove extra spacing at bottom of page */
        body {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        main {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        footer {
            margin-top: 0 !important;
        }

        /* Remove any extra space from page wrapper */
        .page-wrapper {
            margin-bottom: 0 !important;
            padding-bottom: 0 !important;
        }

        /* Force remove min-height that might create white space */
        * {
            min-height: auto !important;
        }

        /* Specifically target common containers */
        .container,
        .wrapper,
        main,
        #app,
        .app {
            min-height: auto !important;
        }

        /* Remove any potential viewport height usage */
        html,
        body {
            height: auto !important;
            min-height: auto !important;
        }

        /* Fixed Header Styles */
        body {
            padding-top: 130px;
            /* Reduced space for fixed header with navigation pulled up */
        }

        /* Header scroll effects */
        header.scrolled {
            background: rgba(4, 56, 157, 0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15) !important;
        }

        /* Smooth scrolling */
        html {
            scroll-behavior: smooth;
        }

        @media (max-width: 768px) {
            body {
                padding-top: 70px;
                /* Smaller padding for mobile */
            }
        }

        .gradient-bg {
            background: linear-gradient(to bottom right, #1e3a8a, #3b82f6);
        }

        .back-to-top {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            display: none;
        }

        .back-to-top.show {
            display: block;
        }

        /* Product Grid - Ensure equal height cards */
        .products.list-products {
            display: grid !important;
            align-items: stretch !important;
        }

        .products.list-products>* {
            height: 100% !important;
            display: flex !important;
            flex-direction: column !important;
        }

        /* Ensure product cards use full height */
        .product-card {
            height: 100% !important;
            display: flex !important;
            flex-direction: column !important;
        }

        .service-card:hover>div:nth-child(2) {
            opacity: 1 !important;
        }

        .pricing-card {
            position: relative;
            overflow: hidden;
            transition: transform 0.3s ease;
        }

        .pricing-card:hover {
            transform: translateY(-5px);
        }

        .glass-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(10px);
        }

        .glass-effect {
            position: absolute;
            inset: 0;
            background: linear-gradient(45deg,
                    transparent 0%,
                    rgba(255, 255, 255, 0.1) 100%);
            transform: translateY(100%);
            transition: transform 0.5s ease;
            z-index: 999999;
        }

        .glass-card:hover .glass-effect {
            transform: translateY(0);
        }

        .ripple-container {
            position: relative;
            width: 400px;
            height: 400px;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .logo-center {
            position: relative;
            z-index: 10;
            width: 350px;
            height: 350px;
            background: radial-gradient(circle, #e8f4fd 0%, #b8dff5 100%);
            border-radius: 50%;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            transition: transform 0.3s ease;
        }

        .logo-center:hover {
            transform: scale(1.05);
        }

        .logo-content {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .logo-icon {
            width: 40px;
            height: 40px;
            background: linear-gradient(45deg, #1e3c72 0%, #2a5298 100%);
            border-radius: 8px;
            display: flex;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .logo-icon::before {
            content: '';
            position: absolute;
            width: 0;
            height: 0;
            border-left: 12px solid transparent;
            border-right: 12px solid transparent;
            border-bottom: 20px solid #00bcd4;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
        }

        .logo-icon::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 2px;
            background: #00bcd4;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            border-radius: 1px;
        }

        .logo-text {
            color: #1e3c72;
            font-weight: bold;
            font-size: 24px;
            letter-spacing: 2px;
        }

        .logo-tagline {
            position: absolute;
            bottom: 40px;
            left: 50%;
            transform: translateX(-50%);
            color: #666;
            font-size: 10px;
            letter-spacing: 1px;
            white-space: nowrap;
        }

        .ripple {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border-radius: 50%;
            border: 2px solid rgba(0, 188, 212, 0.6);
            animation: ripple-animation 3s infinite;
        }

        .ripple:nth-child(1) {
            animation-delay: 0s;
        }

        .ripple:nth-child(2) {
            animation-delay: 0.5s;
        }

        .ripple:nth-child(3) {
            animation-delay: 1s;
        }

        .ripple:nth-child(4) {
            animation-delay: 1.5s;
        }

        .ripple:nth-child(5) {
            animation-delay: 2s;
        }

        .ripple:nth-child(6) {
            animation-delay: 2.5s;
        }

        @keyframes ripple-animation {
            0% {
                width: 0;
                height: 0;
                opacity: 1;
                border-width: 3px;
            }

            50% {
                opacity: 0.7;
                border-width: 2px;
            }

            100% {
                width: 500px;
                height: 500px;
                opacity: 0;
                border-width: 1px;
            }
        }

        .pulse-ring {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 250px;
            height: 250px;
            border-radius: 50%;
            background: radial-gradient(circle, transparent 40%, rgba(0, 188, 212, 0.1) 60%, transparent 80%);
            animation: pulse-ring 2s ease-out infinite;
        }

        @keyframes pulse-ring {
            0% {
                transform: translate(-50%, -50%) scale(0.8);
                opacity: 1;
            }

            100% {
                transform: translate(-50%, -50%) scale(1.2);
                opacity: 0;
            }
        }

        .sound-waves {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 300px;
            height: 300px;
            z-index: 1;
        }

        .wave-line {
            position: absolute;
            background: linear-gradient(90deg, transparent, rgba(0, 188, 212, 0.3), transparent);
            border-radius: 50px;
            animation: wave-move 2s ease-in-out infinite;
        }

        .wave-line:nth-child(1) {
            width: 300px;
            height: 2px;
            top: 50%;
            left: 0;
            animation-delay: 0s;
        }

        .wave-line:nth-child(2) {
            width: 2px;
            height: 300px;
            top: 0;
            left: 50%;
            animation-delay: 0.5s;
        }

        .wave-line:nth-child(3) {
            width: 212px;
            height: 2px;
            top: 25%;
            left: 12.5%;
            transform: rotate(45deg);
            animation-delay: 1s;
        }

        .wave-line:nth-child(4) {
            width: 212px;
            height: 2px;
            top: 25%;
            left: 12.5%;
            transform: rotate(-45deg);
            animation-delay: 1.5s;
        }

        @keyframes wave-move {

            0%,
            100% {
                opacity: 0;
                transform: scale(0.8);
            }

            50% {
                opacity: 1;
                transform: scale(1);
            }
        }

        .floating-particles {
            position: absolute;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 2;
        }

        .particle {
            position: absolute;
            width: 4px;
            height: 4px;
            background: rgba(0, 188, 212, 0.6);
            border-radius: 50%;
            animation: float-particle 4s ease-in-out infinite;
        }

        .particle:nth-child(1) {
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }

        .particle:nth-child(2) {
            top: 80%;
            right: 20%;
            animation-delay: 1s;
        }

        .particle:nth-child(3) {
            bottom: 30%;
            left: 30%;
            animation-delay: 2s;
        }

        .particle:nth-child(4) {
            top: 40%;
            right: 10%;
            animation-delay: 3s;
        }

        @keyframes float-particle {

            0%,
            100% {
                transform: translateY(0) rotate(0deg);
                opacity: 0;
            }

            50% {
                transform: translateY(-20px) rotate(180deg);
                opacity: 1;
            }
        }

        .glow-effect {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 220px;
            height: 220px;
            border-radius: 50%;
            background: radial-gradient(circle, rgba(0, 188, 212, 0.2) 0%, transparent 70%);
            animation: glow-pulse 3s ease-in-out infinite;
            z-index: 5;
        }

        @keyframes glow-pulse {

            0%,
            100% {
                opacity: 0.3;
                transform: translate(-50%, -50%) scale(1);
            }

            50% {
                opacity: 0.8;
                transform: translate(-50%, -50%) scale(1.1);
            }
        }
    </style>
</head>

<body>
    @if (!request()->is('admin') && !request()->is('admin/*'))
        {{-- Frontend Layout --}}
        @include('templates.auvista.blocks.header')

        @hasSection('breadcrumbs')
            @yield('breadcrumbs')
        @else
            @unless (request()->routeIs('products.brand'))
                @include('templates.auvista.blocks.breadcrumb')
            @endunless
        @endif

        <main class="main-content">
            @yield('content')
        </main>

        <livewire:cart-popup />

        @include('templates.auvista.blocks.footer')

        {{-- Fixed Contact Widget - Hiển thị trên tất cả trang --}}
        <div class="fixed top-1/2 right-0 transform -translate-y-1/2 z-50 space-y-3">
            <!-- Hotline Button 1 -->
            <a href="tel:0979998877" class="block">
                <div
                    class="bg-gradient2 text-white px-4 py-[10px] rounded-tl-full rounded-bl-full shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out">
                    <div class="flex items-center gap-3">
                        <div class="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56a.977.977 0 00-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z" />
                            </svg>
                        </div>
                        <div class="text-left">
                            <div class="font-bold opacity-100">
                                {{ __('messages.hotline') }}: 0979998877
                            </div>
                        </div>
                    </div>
                </div>
            </a>

            <!-- Hotline Button 2 -->
            <a href="tel:0988888988" class="block">
                <div
                    class="bg-gradient2 text-white px-4 py-[10px] rounded-tl-full rounded-bl-full shadow-lg hover:shadow-xl transition-all duration-300 ease-in-out">
                    <div class="flex items-center gap-3">
                        <div class="w-5 h-5 bg-white/20 rounded-full flex items-center justify-center flex-shrink-0">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path
                                    d="M20.01 15.38c-1.23 0-2.42-.2-3.53-.56a.977.977 0 00-1.01.24l-1.57 1.97c-2.83-1.35-5.48-3.9-6.89-6.83l1.95-1.66c.27-.28.35-.67.24-1.02-.37-1.11-.56-2.3-.56-3.53 0-.54-.45-.99-.99-.99H4.19C3.65 3 3 3.24 3 3.99 3 13.28 10.73 21 20.01 21c.71 0 .99-.63.99-1.18v-3.45c0-.54-.45-.99-.99-.99z" />
                            </svg>
                        </div>
                        <div class="text-left">
                            <div class="font-bold opacity-100">
                                {{ __('messages.hotline') }}: 0988888988
                            </div>
                        </div>
                    </div>
                </div>
            </a>

            <!-- Zalo Button -->
            <a href="https://zalo.me/0979998877" target="_blank" class="flex justify-end pr-4">
                <img src="{{ asset('images/icon-zalo.png') }}" alt="zalo" class="w-12 h-12" />
            </a>

            <!-- Messenger Button -->
            <a href="https://m.me/avplus.vietnam" target="_blank" class="flex justify-end pr-4">
                <img src="{{ asset('images/icon-messenger.png') }}" alt="messenger" class="w-12 h-12" />
            </a>

            <!-- Email Button -->
            <a href="mailto:<EMAIL>" class="flex justify-end pr-4">
                <img src="{{ asset('images/icon-email.png') }}" alt="email" class="w-12 h-12" />
            </a>
        </div>

        {{-- bằng tailwind --}}
        <button id="backToTop"
            class="back-to-top gradient-bg bg-blue-500 text-white rounded-full p-3 shadow-lg hover:bg-blue-600 transition">
            <i class="fas fa-arrow-up"></i>
        </button>
    @else
        {{-- Admin Layout --}}
        <main class="main-content">
            @yield('content')
        </main>
    @endif

    @livewireScripts
    @stack('scripts')

    {{-- Swiper JS --}}
    <script src="{{ asset('js/swiper-bundle.min.js') }}"></script>
    {{-- Main JS with Swiper initialization --}}
    <script src="{{ asset('js/main.js') }}?v={{ time() }}"></script>
</body>

</html>