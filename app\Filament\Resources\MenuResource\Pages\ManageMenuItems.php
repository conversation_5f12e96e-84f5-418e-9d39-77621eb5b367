<?php

namespace App\Filament\Resources\MenuResource\Pages;

use App\Filament\Resources\MenuResource;
use Filament\Resources\Pages\Page;
use App\Models\Menu;
use App\Models\MenuItem;
use Illuminate\Support\Facades\Route;
use Livewire\Attributes\On;
use Filament\Notifications\Notification;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class ManageMenuItems extends Page
{
    protected static string $resource = MenuResource::class;

    protected static string $view = 'filament.admin.resources.menu-item-resource.pages.manage-menu-items';

    public Menu $menu;

    public $selectedPages = [];
    public $selectedPosts = [];
    public $selectedCategories = [];
    public $selectedBrands = [];
    public $customUrl = '';
    public $customTitle = '';
    public $isMenuReordering = false;
    public $menuItems = [];
    public $selectedMenuItems = [];
    public $editingMenuItem = [];

    public function mount(Menu $menu)
    {
        $this->menu = $menu;
        $this->loadMenuItems();
    }

    protected function loadMenuItems()
    {
        // Làm mới truy vấn để đảm bảo lấy dữ liệu mới nhất từ database
        $items = MenuItem::where('menu_id', $this->menu->id)
            ->orderBy('order')
            ->get(['id', 'title', 'url', 'type', 'order', 'parent_id']);
        
        $this->menuItems = [];
        
        foreach ($items as $item) {
            $this->menuItems[$item->id] = [
                'title' => $item->title,
                'url' => $item->url,
                'type' => $item->type,
                'order' => $item->order,
                'parent_id' => $item->parent_id, // Đảm bảo lưu cả parent_id
            ];
        }
        
        // Đánh dấu cần render lại view
        $this->dispatch('refresh');
    }

    protected function getViewData(): array
    {
        // LƯU Ý: Đảm bảo truy vấn mới nhất từ database thay vì cache
        return [
            'pages' => \App\Models\StaticPage::where('status', 'published')->get(),
            'posts' => \App\Models\Post::where('status', 'published')->get(),
            'categories' => \App\Models\Category::all(), // Lấy tất cả danh mục, không lọc trạng thái
            'brands' => \App\Models\Brand::all(),
            'menuItems' => MenuItem::where('menu_id', $this->menu->id)->orderBy('order')->get(),
        ];
    }

    public function createPageMenuItems()
    {
        foreach ($this->selectedPages as $pageId) {
            $page = \App\Models\StaticPage::find($pageId);
            if ($page) {
                MenuItem::create([
                    'menu_id' => $this->menu->id,
                    'title' => $page->title,
                    'url' => route('link.detail', $page->slug),
                    'type' => 'page',
                    'model_type' => get_class($page),
                    'model_id' => $page->id,
                    'order' => MenuItem::where('menu_id', $this->menu->id)->max('order') + 1,
                    'entity_type' => 'page',
                ]);
            }
        }
        $this->selectedPages = [];
        $this->dispatch('menu-items-updated');
        $this->clearMenuCache();
    }

    public function createPostMenuItems()
    {
        foreach ($this->selectedPosts as $postId) {
            $post = \App\Models\Post::find($postId);
            if ($post) {
                MenuItem::create([
                    'menu_id' => $this->menu->id,
                    'title' => $post->title,
                    'url' => route('link.detail', $post->slug),
                    'type' => 'post',
                    'model_type' => get_class($post),
                    'model_id' => $post->id,
                    'order' => MenuItem::where('menu_id', $this->menu->id)->max('order') + 1,
                    'entity_type' => 'post',
                ]);
            }
        }
        $this->selectedPosts = [];
        $this->dispatch('menu-items-updated');
        $this->clearMenuCache();
    }

    public function createCustomUrlMenuItem()
    {
        if ($this->customUrl && $this->customTitle) {
            MenuItem::create([
                'menu_id' => $this->menu->id,
                'title' => $this->customTitle,
                'url' => $this->customUrl,
                'type' => 'custom',
                'order' => MenuItem::where('menu_id', $this->menu->id)->max('order') + 1,
                'entity_type' => 'custom',
            ]);
            $this->customUrl = '';
            $this->customTitle = '';
            $this->dispatch('menu-items-updated');
            $this->clearMenuCache();
        }
    }

    public function createCategoryMenuItems()
    {
        foreach ($this->selectedCategories as $categoryId) {
            $category = \App\Models\Category::find($categoryId);
            if ($category) {
                MenuItem::create([
                    'menu_id' => $this->menu->id,
                    'title' => $category->name,
                    'url' => route('link.detail', $category->slug),
                    'type' => 'category',
                    'model_type' => get_class($category),
                    'model_id' => $category->id,
                    'order' => MenuItem::where('menu_id', $this->menu->id)->max('order') + 1,
                    'entity_type' => 'category',
                ]);
            }
        }
        $this->selectedCategories = [];
        $this->dispatch('menu-items-updated');
        $this->clearMenuCache();
    }

    public function createBrandMenuItems()
    {
        foreach ($this->selectedBrands as $brandId) {
            $brand = \App\Models\Brand::find($brandId);
            if ($brand) {
                MenuItem::create([
                    'menu_id' => $this->menu->id,
                    'title' => $brand->name,
                    'url' => route('products.brand', $brand->slug),
                    'type' => 'brand',
                    'model_type' => get_class($brand),
                    'model_id' => $brand->id,
                    'order' => MenuItem::where('menu_id', $this->menu->id)->max('order') + 1,
                    'entity_type' => 'brand',
                ]);
            }
        }
        $this->selectedBrands = [];
        $this->dispatch('menu-items-updated');
        $this->clearMenuCache();
    }

    public function editMenuItem($menuItemId)
    {
        $menuItem = MenuItem::find($menuItemId);
        
        if ($menuItem && isset($this->menuItems[$menuItemId])) {
            $menuItem->title = $this->menuItems[$menuItemId]['title'] ?? $menuItem->title;
            
            if ($menuItem->type === 'custom' && isset($this->menuItems[$menuItemId]['url'])) {
                $menuItem->url = $this->menuItems[$menuItemId]['url'];
            }
            
            $menuItem->save();
            
            Notification::make()
                ->title('Mục menu đã được cập nhật')
                ->body('Cập nhật thành công')
                ->success()
                ->send();
            
            $this->dispatch('menu-items-updated');
            $this->clearMenuCache();
        }
    }
    
    public function deleteMenuItem($menuItemId)
    {
        $menuItem = MenuItem::find($menuItemId);
        
        if ($menuItem) {
            $menuItem->delete();
            
            Notification::make()
                ->title('Mục menu đã được xóa')
                ->body('Xóa thành công')
                ->success()
                ->send();
            
            $this->dispatch('close-modal', id: 'delete-menu-item-' . $menuItemId);
            $this->dispatch('menu-items-updated');
            $this->loadMenuItems();
            $this->clearMenuCache();
        }
    }

    #[On('toggle_reordering')]
    public function toggleReordering()
    {
        $this->isMenuReordering = !$this->isMenuReordering;
    }

    public function saveMenu()
    {
        try {
            // Hiện tại, thứ tự đã được lưu thông qua updateMenuStructure
            // Đảm bảo xóa cache cho menu này để hiển thị đúng ở frontend
            $this->clearMenuCache();
            
            // Cách thủ công để đảm bảo thứ tự đúng nếu có lỗi
            $menuItems = MenuItem::where('menu_id', $this->menu->id)
                ->orderBy('order')
                ->get();
            
            // Cập nhật lại thứ tự và parent_id để đảm bảo không có lỗi
            DB::transaction(function() use ($menuItems) {
                foreach ($menuItems as $index => $item) {
                    // Lưu thứ tự và đảm bảo parent_id NULL (không phải 0)
                    $parent_id = $item->parent_id;
                    if ($parent_id === 0) {
                        $parent_id = null;
                    }
                    
                    MenuItem::where('id', $item->id)->update([
                        'order' => $index + 1,
                        'parent_id' => $parent_id
                    ]);
                }
            });
            
            // Force xóa cache liên quan đến menu
            Cache::forget('menu_' . $this->menu->slug . '_model');
            Cache::forget('menu_' . $this->menu->slug . '_items');
            Cache::forget('menu_' . $this->menu->location . '_model');
            Cache::forget('menu_' . $this->menu->location . '_items');
            
            Notification::make()
                ->title('Menu đã được lưu')
                ->body('Các thay đổi đã được lưu thành công.')
                ->success()
                ->send();
                
            // Thay vì redirect, chỉ cần tải lại trang hiện tại
            $this->loadMenuItems();
            return;
        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi khi lưu menu')
                ->body('Đã xảy ra lỗi: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function updateMenuItem($menuItemId)
    {
        try {
            $menuItem = MenuItem::findOrFail($menuItemId);

            // Validate that the menu item belongs to this menu
            if ($menuItem->menu_id !== $this->menu->id) {
                throw new \Exception('Menu item không thuộc về menu này.');
            }

            // Update the menu item with form data
            $menuItem->update([
                'title' => $this->editingMenuItem['title'] ?? $menuItem->title,
                'url' => $this->editingMenuItem['url'] ?? $menuItem->url,
                'navigation_label' => $this->editingMenuItem['navigation_label'] ?? null,
                'css_classes' => $this->editingMenuItem['css_classes'] ?? null,
                'target' => $this->editingMenuItem['target'] ?? '_self',
                'description' => $this->editingMenuItem['description'] ?? null,
            ]);

            // Clear cache
            $this->clearMenuCache();

            // Reset editing state
            $this->editingMenuItem = [];

            Notification::make()
                ->title('Cập nhật thành công')
                ->body('Menu item đã được cập nhật.')
                ->success()
                ->send();

            $this->loadMenuItems();

        } catch (\Exception $e) {
            Notification::make()
                ->title('Lỗi cập nhật')
                ->body('Đã xảy ra lỗi: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function deleteMenu()
    {
        $this->menu->delete();
        $this->redirect(MenuResource::getUrl());
    }
    
    public function updateMenuItemsOrder($items)
    {
        foreach ($items as $item) {
            if (isset($item['id']) && isset($item['order'])) {
                $menuItem = MenuItem::find($item['id']);
                if ($menuItem) {
                    $menuItem->order = $item['order'];
                    $menuItem->save();
                }
            }
        }
        
        Notification::make()
            ->title('Thứ tự menu đã được cập nhật')
            ->body('Cập nhật thành công')
            ->success()
            ->send();
        
        $this->loadMenuItems();
    }

    /**
     * Cập nhật cả thứ tự và mối quan hệ cha-con của các mục menu
     */
    public function updateMenuStructure($items)
    {
        foreach ($items as $item) {
            if (isset($item['id']) && isset($item['order'])) {
                $menuItem = MenuItem::find($item['id']);
                if ($menuItem) {
                    $menuItem->order = $item['order'];
                    
                    // Cập nhật quan hệ cha-con - xử lý đúng giá trị null
                    if (isset($item['parent_id']) && !empty($item['parent_id'])) {
                        // Chuyển đổi sang integer
                        $menuItem->parent_id = (int) $item['parent_id'];
                    } else {
                        // Đặt giá trị thực sự là NULL trong cơ sở dữ liệu
                        $menuItem->parent_id = null;
                    }
                    
                    $menuItem->save();
                }
            }
        }
        
        Notification::make()
            ->title('Cấu trúc menu đã được cập nhật')
            ->body('Thứ tự và quan hệ cha-con đã được cập nhật thành công')
            ->success()
            ->send();
        
        $this->loadMenuItems();
        $this->dispatch('menu-items-updated');
        $this->clearMenuCache();
    }

    #[On('deleteBulkMenuItems')]
    public function deleteBulkMenuItems()
    {
        if (empty($this->selectedMenuItems)) {
            Notification::make()
                ->title('Chưa chọn mục nào')
                ->body('Vui lòng chọn ít nhất một mục menu để xóa')
                ->warning()
                ->send();
                
            return;
        }
        
        $count = count($this->selectedMenuItems);
        MenuItem::whereIn('id', $this->selectedMenuItems)->delete();
        
        Notification::make()
            ->title('Đã xóa ' . $count . ' mục menu')
            ->body('Các mục menu đã được xóa thành công')
            ->success()
            ->send();
            
        $this->selectedMenuItems = [];
        $this->dispatch('close-modal', id: 'bulk-delete-menu-items');
        $this->dispatch('menu-items-updated');
        $this->loadMenuItems();
        $this->clearMenuCache();
    }

    /**
     * Tạo quan hệ cha-con giữa 2 menu items
     */
    #[On('createParentChildRelation')]
    public function createParentChildRelation($childId, $parentId)
    {
        try {
            \Log::info('=== CREATE PARENT-CHILD RELATION ===');
            \Log::info('Child ID: ' . $childId);
            \Log::info('Parent ID: ' . $parentId);

            $childItem = MenuItem::find($childId);
            $parentItem = MenuItem::find($parentId);

            if (!$childItem || !$parentItem) {
                throw new \Exception('Menu item không tồn tại');
            }

            // Kiểm tra để tránh vòng lặp cha-con
            if ($this->wouldCreateCircularReference($childId, $parentId)) {
                throw new \Exception('Không thể tạo quan hệ cha-con vòng lặp');
            }

            // Cập nhật parent_id cho child item
            $childItem->parent_id = $parentId;

            // Đặt order cho child item (cuối cùng trong danh sách con)
            $maxOrder = MenuItem::where('parent_id', $parentId)->max('order') ?? 0;
            $childItem->order = $maxOrder + 1;

            $childItem->save();

            \Log::info('Parent-child relation created successfully');

            Notification::make()
                ->title('Đã tạo quan hệ cha-con')
                ->body("'{$childItem->title}' đã trở thành menu con của '{$parentItem->title}'")
                ->success()
                ->send();

            $this->loadMenuItems();
            $this->dispatch('menu-items-updated');
            $this->clearMenuCache();

        } catch (\Exception $e) {
            \Log::error('Error creating parent-child relation: ' . $e->getMessage());

            Notification::make()
                ->title('Lỗi tạo quan hệ cha-con')
                ->body($e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Kiểm tra xem việc tạo quan hệ cha-con có tạo vòng lặp không
     */
    private function wouldCreateCircularReference($childId, $parentId)
    {
        // Kiểm tra xem parentId có phải là con của childId không
        $parent = MenuItem::find($parentId);

        while ($parent && $parent->parent_id) {
            if ($parent->parent_id == $childId) {
                return true; // Tạo vòng lặp
            }
            $parent = MenuItem::find($parent->parent_id);
        }

        return false;
    }

    /**
     * Lưu thứ tự menu sau khi kéo thả
     */
    #[On('reorderMenuItem')]
    public function reorderMenuItems($items)
    {
        $this->isMenuReordering = true;
        
        try {
            // Ghi log để debug
            \Illuminate\Support\Facades\Log::info('Menu items to reorder: ', $items);
            
            // Đảm bảo items là mảng
            if (!is_array($items)) {
                throw new \Exception('Items phải là mảng');
            }
            
            // Reset lại thứ tự menu để đảm bảo cập nhật đúng
            DB::table('menu_items')
                ->where('menu_id', $this->menu->id)
                ->update(['parent_id' => null]);
            
            // Thiết lập lại cấu trúc cha-con
            $this->saveMenuOrder($items['items'] ?? $items);
            
            // Xóa cache để áp dụng ngay lập tức
            Cache::flush();
            
            // Tải lại dữ liệu menu từ database
            $this->menu = Menu::with('items.children')->find($this->menu->id);
            
            // Cập nhật danh sách menu items trong component
            $this->loadMenuItems();
            
            // Thông báo thành công
            Notification::make()
                ->title('Menu đã được cập nhật')
                ->success()
                ->send();
            
            // Gửi sự kiện JavaScript để cập nhật UI mà không tải lại trang
            $this->dispatch('update-menu-structure');
            
        } catch (\Exception $e) {
            // Ghi log lỗi để debug
            \Illuminate\Support\Facades\Log::error('Error reordering menu: ' . $e->getMessage(), [
                'trace' => $e->getTraceAsString(),
                'items' => $items
            ]);
            
            Notification::make()
                ->title('Có lỗi xảy ra')
                ->body($e->getMessage())
                ->danger()
                ->send();
                
            $this->isMenuReordering = false;
        }
    }

    /**
     * Lưu thứ tự menu sau khi kéo thả
     */
    public function saveMenuOrder($items, $parentId = null)
    {
        // Debug log
        \Illuminate\Support\Facades\Log::info('saveMenuOrder called with items: ', [
            'items' => $items,
            'parentId' => $parentId
        ]);
        
        foreach ($items as $index => $item) {
            if (!isset($item['id'])) {
                \Illuminate\Support\Facades\Log::warning('Menu item missing ID', ['item' => $item]);
                continue;
            }
            
            $menuItem = MenuItem::find($item['id']);
            if ($menuItem) {
                // Đảm bảo parent_id là NULL hoặc ID hợp lệ, không phải 0
                $menuParentId = $parentId;
                if ($menuParentId === 0) {
                    $menuParentId = null;
                }
                
                // Update cả 2 trường một lúc để đảm bảo tính nhất quán
                DB::table('menu_items')
                    ->where('id', $menuItem->id)
                    ->update([
                        'parent_id' => $menuParentId,
                        'order' => $index + 1
                    ]);
                
                // Log thành công
                \Illuminate\Support\Facades\Log::info('Updated menu item: ', [
                    'id' => $menuItem->id,
                    'parent_id' => $menuParentId,
                    'order' => $index + 1
                ]);

                if (isset($item['children']) && count($item['children']) > 0) {
                    $this->saveMenuOrder($item['children'], $menuItem->id);
                }
            } else {
                \Illuminate\Support\Facades\Log::warning('Menu item not found: ' . $item['id']);
            }
        }
    }

    /**
     * Xóa cache menu để áp dụng thay đổi ngay lập tức trên frontend
     */
    protected function clearMenuCache()
    {
        \Illuminate\Support\Facades\Cache::forget('menu_' . $this->menu->slug);
        \Illuminate\Support\Facades\Cache::forget('menu_' . $this->menu->location);
        
        Notification::make()
            ->title('Cache menu đã được xóa')
            ->body('Các thay đổi sẽ hiển thị ngay lập tức trên trang web')
            ->success()
            ->send();
    }
} 