@extends('templates.auvista.layouts.default')

@section('title', __('messages.contact') . ' - AVPlus')
@section('meta_description', __('messages.contact_description'))

@section('content')
<main id="main" class="site-main">
    <article class="page-wrapper page-content">
        <section class="pt-2 lg:pt-20">
            <h1 class="container pb-8 text-2xl lg:text-[38px]/[1.4] font-bold text-primary-base">{{ __('messages.contact_title') }}</h1>
            <div class="">
                <iframe
                    src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d3724.426701690278!2d105.78524797571922!3d21.01560598822945!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x3135abe8109f597f%3A0x7c6b089bcd5aa558!2zQ8O0bmcgVHkgQ-G7lSBQaOG6p24gQVYgUGx1cw!5e0!3m2!1svi!2s!4v1750227527636!5m2!1svi!2s"
                    width="100%"
                    height="auto"
                    class="aspect-[1/0.5] md:aspect-[1/0.2375]"
                    style="border: 0"
                    allowfullscreen=""
                    loading="lazy"
                    referrerpolicy="no-referrer-when-downgrade">
                </iframe>
            </div>
        </section>
        
        <section class="relative bg-cover bg-no-repeat bg-center" style="background-image: url('{{ asset('images/bg-image-contact.png') }}');">
            <div class="absolute inset-0 bg-gradient3"></div>
            <div class="container pt-10 pb-10 lg:pt-6 lg:pb-6 relative z-10">
                <div class="flex justify-between flex-col md:flex-row items-start gap-x-8 gap-y-6">
                    <div class="w-full md:w-[50%]">
                        <ul class="text-white">
                            <li class="text-xl font-bold pb-4 uppercase">AV PLUS</li>
                            <li class="md:text-lg pb-3 relative pl-[30px]">
                                <span class="absolute left-0 top-[2px] w-5 h-5">
                                    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M10.0004 12.3083C8.22539 12.3083 6.77539 10.8666 6.77539 9.0833C6.77539 7.29997 8.22539 5.86664 10.0004 5.86664C11.7754 5.86664 13.2254 7.30831 13.2254 9.09164C13.2254 10.875 11.7754 12.3083 10.0004 12.3083ZM10.0004 7.11664C8.91706 7.11664 8.02539 7.99997 8.02539 9.09164C8.02539 10.1833 8.90872 11.0666 10.0004 11.0666C11.0921 11.0666 11.9754 10.1833 11.9754 9.09164C11.9754 7.99997 11.0837 7.11664 10.0004 7.11664Z" fill="white"/>
                                        <path d="M9.99941 19.4667C8.76608 19.4667 7.52441 19 6.55775 18.075C4.09941 15.7084 1.38275 11.9334 2.40775 7.44169C3.33275 3.36669 6.89108 1.54169 9.99941 1.54169C9.99941 1.54169 9.99941 1.54169 10.0077 1.54169C13.1161 1.54169 16.6744 3.36669 17.5994 7.45002C18.6161 11.9417 15.8994 15.7084 13.4411 18.075C12.4744 19 11.2327 19.4667 9.99941 19.4667ZM9.99941 2.79169C7.57441 2.79169 4.45775 4.08335 3.63275 7.71669C2.73275 11.6417 5.19941 15.025 7.43275 17.1667C8.87441 18.5584 11.1327 18.5584 12.5744 17.1667C14.7994 15.025 17.2661 11.6417 16.3827 7.71669C15.5494 4.08335 12.4244 2.79169 9.99941 2.79169Z" fill="white"/>
                                    </svg>
                                </span>
                                {{ __('messages.address') }}: {{ getSetting('address', 'Trụ sở chính 88 Thái Hà, Hà Nội') }}
                            </li>
                            <li class="md:text-lg pb-3 relative pl-[30px]">
                                <span class="absolute left-0 top-[2px] w-5 h-5">
                                    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M18.3327 19.4583H1.66602C1.32435 19.4583 1.04102 19.175 1.04102 18.8333C1.04102 18.4916 1.32435 18.2083 1.66602 18.2083H18.3327C18.6744 18.2083 18.9577 18.4916 18.9577 18.8333C18.9577 19.175 18.6744 19.4583 18.3327 19.4583Z" fill="white"/>
                                        <path d="M3.08398 18.8333H1.83398L1.87565 8.80832C1.87565 8.09998 2.20065 7.44167 2.75898 7.00834L8.59232 2.46666C9.41732 1.825 10.5757 1.825 11.409 2.46666L17.2423 7C17.7923 7.43333 18.1257 8.10832 18.1257 8.80832V18.8333H16.8757V8.81666C16.8757 8.49999 16.7257 8.19167 16.4757 7.99167L10.6423 3.45833C10.2673 3.16666 9.74232 3.16666 9.35899 3.45833L3.52566 8.00001C3.27566 8.19168 3.12565 8.49999 3.12565 8.81666L3.08398 18.8333Z" fill="white"/>
                                        <path d="M14.1673 19.4584H5.83398C5.49232 19.4584 5.20898 19.175 5.20898 18.8334V10.9167C5.20898 9.88335 6.05065 9.04169 7.08398 9.04169H12.9173C13.9507 9.04169 14.7923 9.88335 14.7923 10.9167V18.8334C14.7923 19.175 14.509 19.4584 14.1673 19.4584ZM6.45898 18.2084H13.5423V10.9167C13.5423 10.575 13.259 10.2917 12.9173 10.2917H7.08398C6.74232 10.2917 6.45898 10.575 6.45898 10.9167V18.2084Z" fill="white"/>
                                        <path d="M8.33398 15.9167C7.99232 15.9167 7.70898 15.6334 7.70898 15.2917V14.0417C7.70898 13.7 7.99232 13.4167 8.33398 13.4167C8.67565 13.4167 8.95898 13.7 8.95898 14.0417V15.2917C8.95898 15.6334 8.67565 15.9167 8.33398 15.9167Z" fill="white"/>
                                        <path d="M11.25 7.375H8.75C8.40833 7.375 8.125 7.09167 8.125 6.75C8.125 6.40833 8.40833 6.125 8.75 6.125H11.25C11.5917 6.125 11.875 6.40833 11.875 6.75C11.875 7.09167 11.5917 7.375 11.25 7.375Z" fill="white"/>
                                    </svg>
                                </span>
                                {{ __('messages.office_address') }}: {{ getSetting('address', '123 Chùa Bộc, Hà Nội') }}
                            </li>
                            <li class="md:text-lg pb-3 relative pl-[30px]">
                                <span class="absolute left-0 top-[2px] w-5 h-5">
                                    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14.541 19.4584C13.5993 19.4584 12.6077 19.2334 11.5827 18.8C10.5827 18.375 9.57435 17.7917 8.59102 17.0834C7.61602 16.3667 6.67435 15.5667 5.78268 14.6917C4.89935 13.8 4.09935 12.8584 3.39102 11.8917C2.67435 10.8917 2.09935 9.89169 1.69102 8.92502C1.25768 7.89169 1.04102 6.89169 1.04102 5.95002C1.04102 5.30002 1.15768 4.68335 1.38268 4.10835C1.61602 3.51669 1.99102 2.96669 2.49935 2.49169C3.14102 1.85835 3.87435 1.54169 4.65768 1.54169C4.98268 1.54169 5.31602 1.61669 5.59935 1.75002C5.92435 1.90002 6.19935 2.12502 6.39935 2.42502L8.33268 5.15002C8.50768 5.39169 8.64102 5.62502 8.73268 5.85835C8.84102 6.10835 8.89935 6.35835 8.89935 6.60002C8.89935 6.91669 8.80768 7.22502 8.63268 7.51669C8.50768 7.74169 8.31602 7.98335 8.07435 8.22502L7.50768 8.81669C7.51602 8.84169 7.52435 8.85835 7.53268 8.87502C7.63268 9.05002 7.83268 9.35002 8.21602 9.80002C8.62435 10.2667 9.00768 10.6917 9.39102 11.0834C9.88268 11.5667 10.291 11.95 10.6743 12.2667C11.1494 12.6667 11.4577 12.8667 11.641 12.9584L11.6243 13L12.2327 12.4C12.491 12.1417 12.741 11.95 12.9827 11.825C13.441 11.5417 14.0244 11.4917 14.6077 11.7334C14.8244 11.825 15.0577 11.95 15.3077 12.125L18.0744 14.0917C18.3827 14.3 18.6077 14.5667 18.741 14.8834C18.866 15.2 18.9243 15.4917 18.9243 15.7834C18.9243 16.1834 18.8327 16.5834 18.6577 16.9584C18.4827 17.3334 18.266 17.6584 17.991 17.9584C17.516 18.4834 16.9993 18.8584 16.3994 19.1C15.8244 19.3334 15.1994 19.4584 14.541 19.4584ZM4.65768 2.79169C4.19935 2.79169 3.77435 2.99169 3.36602 3.39169C2.98268 3.75002 2.71602 4.14169 2.54935 4.56669C2.37435 5.00002 2.29102 5.45835 2.29102 5.95002C2.29102 6.72502 2.47435 7.56669 2.84102 8.43335C3.21602 9.31669 3.74102 10.2334 4.40768 11.15C5.07435 12.0667 5.83268 12.9584 6.66602 13.8C7.49935 14.625 8.39935 15.3917 9.32435 16.0667C10.2243 16.725 11.1493 17.2584 12.066 17.6417C13.491 18.25 14.8244 18.3917 15.9243 17.9334C16.3493 17.7584 16.7243 17.4917 17.066 17.1084C17.2577 16.9 17.4077 16.675 17.5327 16.4084C17.6327 16.2 17.6827 15.9834 17.6827 15.7667C17.6827 15.6334 17.6577 15.5 17.591 15.35C17.566 15.3 17.516 15.2084 17.3577 15.1L14.591 13.1334C14.4243 13.0167 14.2743 12.9334 14.1327 12.875C13.9493 12.8 13.8743 12.725 13.591 12.9C13.4243 12.9834 13.2743 13.1084 13.1077 13.275L12.4743 13.9C12.1494 14.2167 11.6494 14.2917 11.266 14.15L11.041 14.05C10.6993 13.8667 10.2993 13.5834 9.85768 13.2084C9.45768 12.8667 9.02435 12.4667 8.49935 11.95C8.09102 11.5334 7.68268 11.0917 7.25768 10.6C6.86602 10.1417 6.58268 9.75002 6.40768 9.42502L6.30768 9.17502C6.25768 8.98335 6.24102 8.87502 6.24102 8.75835C6.24102 8.45835 6.34935 8.19169 6.55768 7.98335L7.18268 7.33335C7.34935 7.16669 7.47435 7.00835 7.55768 6.86669C7.62435 6.75835 7.64935 6.66669 7.64935 6.58335C7.64935 6.51669 7.62435 6.41669 7.58268 6.31669C7.52435 6.18335 7.43268 6.03335 7.31602 5.87502L5.38268 3.14169C5.29935 3.02502 5.19935 2.94169 5.07435 2.88335C4.94102 2.82502 4.79935 2.79169 4.65768 2.79169ZM11.6243 13.0084L11.491 13.575L11.716 12.9917C11.6743 12.9834 11.641 12.9917 11.6243 13.0084Z" fill="white"/>
                                    </svg>
                                </span>
                                {{ __('messages.phone') }}: {{ getSetting('phone', '0963173261') }}
                            </li>
                            <li class="md:text-lg relative pl-[30px]">
                                <span class="absolute left-0 top-[2px] w-5 h-5">
                                    <svg width="20" height="21" viewBox="0 0 20 21" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M14.166 18.2084H5.83268C2.79102 18.2084 1.04102 16.4584 1.04102 13.4167V7.58335C1.04102 4.54169 2.79102 2.79169 5.83268 2.79169H14.166C17.2077 2.79169 18.9577 4.54169 18.9577 7.58335V13.4167C18.9577 16.4584 17.2077 18.2084 14.166 18.2084ZM5.83268 4.04169C3.44935 4.04169 2.29102 5.20002 2.29102 7.58335V13.4167C2.29102 15.8 3.44935 16.9584 5.83268 16.9584H14.166C16.5493 16.9584 17.7077 15.8 17.7077 13.4167V7.58335C17.7077 5.20002 16.5493 4.04169 14.166 4.04169H5.83268Z" fill="white"/>
                                        <path d="M9.999 11.225C9.299 11.225 8.59067 11.0083 8.049 10.5666L5.44067 8.48331C5.174 8.26664 5.124 7.87497 5.34067 7.60831C5.55734 7.34164 5.94901 7.29164 6.21567 7.50831L8.824 9.59164C9.45733 10.1 10.5323 10.1 11.1657 9.59164L13.774 7.50831C14.0407 7.29164 14.4407 7.33331 14.649 7.60831C14.8657 7.87497 14.824 8.27498 14.549 8.48331L11.9407 10.5666C11.4073 11.0083 10.699 11.225 9.999 11.225Z" fill="white"/>
                                    </svg>
                                </span>
                                {{ __('messages.email') }}: {{ getSetting('email', '<EMAIL>') }}
                            </li>
                        </ul>
                    </div>
                    
                    <div class="w-full md:w-[50%] text-white">
                        <h3 class="mb-6 text-xl font-bold">{{ __('messages.send_message') }}</h3>
                        
                        @if(session('success'))
                            <div class="bg-green-500 text-white p-3 rounded mb-4">
                                {{ session('success') }}
                            </div>
                        @endif
                        
                        @if($errors->any())
                            <div class="bg-red-500 text-white p-3 rounded mb-4">
                                <ul>
                                    @foreach($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif
                        
                        <form action="{{ route('contact.send') }}" method="POST" class="max-w-3xl mx-auto grid grid-cols-1 md:grid-cols-2 gap-4">
                            @csrf
                            <input type="text" name="name" placeholder="{{ __('messages.full_name') }} *" value="{{ old('name') }}" class="w-full rounded-full border border-white bg-transparent text-white placeholder-white h-10 px-3 py-2 focus:outline-none focus:border-white" required />

                            <input type="email" name="email" placeholder="{{ __('messages.email') }} *" value="{{ old('email') }}" class="w-full rounded-full border border-white bg-transparent text-white placeholder-white h-10 px-3 py-2 focus:outline-none focus:border-white" />

                            <input type="text" name="phone" placeholder="{{ __('messages.phone') }} *" value="{{ old('phone') }}" class="w-full rounded-full border border-white bg-transparent text-white placeholder-white h-10 px-3 py-2 focus:outline-none focus:border-white" />

                            <div class="col-span-full">
                                <textarea name="message" placeholder="{{ __('messages.message') }} *" rows="4" class="w-full rounded-xl border border-white bg-transparent text-white placeholder-white px-3 py-2 focus:outline-none focus:border-white">{{ old('message') }}</textarea>
                            </div>

                            <div class="col-span-full text-center">
                                <button type="submit" class="rounded-full text-white bg-gradient2 transition-all px-11 py-1 h-11 font-medium hover:shadow-xl hover:text-primary-base hover:-translate-y-1 active:translate-y-0.5">
                                    {{ __('messages.send_message') }}
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>
        
        <div class="">
            <a href="#">
                <img
                    src="{{ asset('images/banner-contact.jpg') }}"
                    alt="Banner Contact"
                    class="w-full h-auto object-cover"
                />
            </a>
        </div>
    </article>
</main>
@endsection