<?php

namespace App\Http\Controllers;

use App\Models\Product;
use App\Models\Category;
use App\Models\Brand;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use App\Traits\SeoTrait;
use Illuminate\Support\Facades\App;

use App\Helpers\TranslationHelper;

class ProductController extends Controller
{
    use SeoTrait;

    /**
     * Hiển thị danh sách sản phẩm
     */
    public function index(Request $request)
    {
        // Debug để kiểm tra phương thức được gọi
        //dd('ProductController@index is called');

        $seoData = $this->getDataSeo([
            'title' => 'Sản phẩm - Auvista',
            'meta_description' => 'Khám phá các sản phẩm âm thanh chuyên nghiệp của Auvista',
            'meta_keywords' => 'sản phẩm auvista, âm thanh, hội nghị, trình chiếu'
        ]);

        $query = Product::with(['category', 'brand'])
            ->where('status', true);

        // Filter by category
        if ($request->has('category') && !empty($request->category)) {
            $query->whereIn('category_id', $request->category);
        }

        // Filter by brand
        if ($request->has('brand') && !empty($request->brand)) {
            $query->whereHas('brand', function ($q) use ($request) {
                $q->whereIn('slug', $request->brand);
            });
        }

        // Filter by price range (checkboxes)
        if ($request->has('price') && !empty($request->price)) {
            $priceRanges = $request->price;
            $query->where(function ($q) use ($priceRanges) {
                foreach ($priceRanges as $priceRange) {
                    switch ($priceRange) {
                        case '0-500000':
                            $q->orWhere('price', '<', 500000);
                            break;
                        case '500000-1000000':
                            $q->orWhereBetween('price', [500000, 1000000]);
                            break;
                        case '1000000-3000000':
                            $q->orWhereBetween('price', [1000000, 3000000]);
                            break;
                        case '3000000-20000000':
                            $q->orWhereBetween('price', [3000000, 20000000]);
                            break;
                    }
                }
            });
        }

        // Filter by price range (radio buttons)
        if ($request->has('price_range') && !empty($request->price_range)) {
            $priceRange = $request->price_range;
            switch ($priceRange) {
                case '20000000+':
                    $query->where('price', '>', 20000000);
                    break;
                case '10000000-20000000':
                    $query->whereBetween('price', [10000000, 20000000]);
                    break;
                case '5000000+':
                    $query->where('price', '>', 5000000);
                    break;
            }
        }

        // Apply sorting
        switch ($request->get('sort', 'default')) {
            case 'popularity':
                $query->orderByDesc('views');
                break;
            case 'best-seller':
                $query->orderByDesc('sold_count');
                break;
            case 'rating':
                $query->orderByDesc('rating');
                break;
            case 'price-asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price-desc':
                $query->orderBy('price', 'desc');
                break;
            default:
                $query->latest(); // Default sorting
        }

        $products = $query->paginate(12)->withQueryString();

        $categories = Category::withCount('products')->where('parent_id', null)->get();
        $breadcrumbs = [
            ['title' => '<i class="fas fa-home"></i><span class="ms-1">Trang chủ</span>', 'url' => route('home')],
            ['title' => 'Sản phẩm', 'url' => route('products.index')]
        ];
        // Thêm brands và productsCount cho filter
        $brands = Brand::withCount('products')->orderBy('name')->get();
        $productsCount = $query->count();
        
        // Tạo object $page giả để tương thích với view template
        $page = (object) [
            'slug' => 'san-pham',
            'title' => 'Sản phẩm',
            'id' => null
        ];
        
        // Thêm selected categories từ request
        $selectedCategories = $request->get('category', []);

        return view('templates.auvista.pages.products', compact('products', 'categories', 'brands', 'breadcrumbs', 'productsCount', 'page', 'selectedCategories') + $seoData);
    }

    /**
     * Hiển thị chi tiết sản phẩm
     */
    public function show($slug)
    {
        $currentLocale = App::getLocale();
        
        // Debug: Log current locale and slug
        Log::info('ProductController@show', [
            'slug' => $slug,
            'locale' => $currentLocale,
            'available_locales' => ['vi', 'en']
        ]);

        // Bước 1: Tìm sản phẩm theo slug và ngôn ngữ hiện tại
        $product = Product::with(['category', 'brand', 'reviews'])
            ->where('slug', $slug)
            ->where('lang', $currentLocale)
            ->first();

        // Bước 2: Nếu không tìm thấy, tìm sản phẩm với slug đó ở ngôn ngữ khác
        if (!$product) {
            $productOtherLang = Product::where('slug', $slug)->first();
            
            if ($productOtherLang) {
                Log::info('Product found in other language', [
                    'product_id' => $productOtherLang->id,
                    'product_lang' => $productOtherLang->lang,
                    'product_sku' => $productOtherLang->sku
                ]);
                
                // Bước 3: Tìm version tương ứng ở ngôn ngữ hiện tại theo SKU hoặc tên tương tự
                $correctProduct = Product::where('sku', $productOtherLang->sku)
                    ->where('lang', $currentLocale)
                    ->first();
                
                // Nếu không tìm thấy theo SKU, thử tìm theo tên tương tự
                if (!$correctProduct) {
                    // Lấy từ khóa chính từ tên sản phẩm (loại bỏ stop words)
                    $productName = $productOtherLang->name;
                    $keywords = [];
                    
                    // Tìm các từ khóa quan trọng từ tên sản phẩm
                    // Tìm brand name (từ đầu tiên)
                    if (preg_match('/^([A-Za-z]+)/', $productName, $matches)) {
                        $keywords[] = $matches[1]; // Ví dụ: "Poly"
                    }
                    
                    // Tìm model number (pattern: Studio X50, Rally Bar, etc.)
                    if (preg_match('/([A-Za-z]+\s+[A-Z0-9]+)/', $productName, $matches)) {
                        $keywords[] = $matches[1]; // Ví dụ: "Studio X50"
                    }
                    
                    // Tìm số model riêng (X50, X30, etc.)
                    if (preg_match('/([A-Z]\d+)/', $productName, $matches)) {
                        $keywords[] = $matches[1]; // Ví dụ: "X50"
                    }
                    
                    if (!empty($keywords)) {
                        $correctProduct = Product::where('lang', $currentLocale)
                            ->where(function($query) use ($keywords) {
                                foreach ($keywords as $index => $keyword) {
                                    if ($index === 0) {
                                        $query->where('name', 'like', '%' . $keyword . '%');
                                    } else {
                                        $query->orWhere('name', 'like', '%' . $keyword . '%');
                                    }
                                }
                            })
                            ->first();
                    }
                }
                
                if ($correctProduct) {
                    Log::info('Redirecting to correct language version', [
                        'from_slug' => $slug,
                        'to_slug' => $correctProduct->slug,
                        'locale' => $currentLocale
                    ]);
                    // Redirect sang URL đúng ngôn ngữ
                    return redirect()->route('products.show', $correctProduct->slug);
                }
            }
            
            // Bước 4: Nếu vẫn không tìm thấy, thử fallback sang vi
            $product = Product::with(['category', 'brand', 'reviews'])
                ->where('slug', $slug)
                ->where('lang', 'vi')
                ->first();
        }

        // Bước 5: Nếu vẫn không tìm thấy, 404
        if (!$product) {
            Log::warning('Product not found', [
                'slug' => $slug,
                'locale' => $currentLocale
            ]);
            abort(404);
        }

        Log::info('Product found', [
            'product_id' => $product->id,
            'product_lang' => $product->lang,
            'product_name' => $product->name
        ]);

        // Lấy sản phẩm liên quan (cùng danh mục, trừ sản phẩm hiện tại)
        $related_products = Product::where('category_id', $product->category_id)
            ->where('id', '!=', $product->id)
            ->where('status', true)
            ->limit(4)
            ->get();
            
        // Kiểm tra sản phẩm có bất kỳ đánh giá nào không
        $hasReviews = $product->reviews()->count() > 0;

        // Khởi tạo biến $reviews và $distribution với giá trị mặc định
        $reviews = collect([]);
        $distribution = [5 => 0, 4 => 0, 3 => 0, 2 => 0, 1 => 0];

        // Nếu có đánh giá, lấy danh sách đánh giá và tính phân phối
        if ($hasReviews) {
            // Sắp xếp đánh giá theo thời gian và lấy reviews đã được phê duyệt
            $reviews = $product->approvedReviews()->get()
                ->sortByDesc('created_at')
                ->values();

            // Tính phân phối rating
            foreach ($reviews as $review) {
                $rating = min(5, max(1, $review->rating));
                $distribution[$rating]++;
            }

            // Chuyển đổi sang phần trăm
            $reviewsCount = $reviews->count();
            if ($reviewsCount > 0) {
                foreach ($distribution as $rating => $count) {
                    $distribution[$rating] = round(($count / $reviewsCount) * 100, 1);
                }
            }
        }

        $reviewsCount = $product->reviews()->count();
        $averageRating = $product->average_rating;

        // Tạo breadcrumbs
        $breadcrumbs = [
            'items' => [
                [
                    'name' => __('Trang chủ'),
                    'url' => route('home'),
                    'active' => false
                ]
            ]
        ];

        // Thêm danh mục vào breadcrumb nếu có và slug hợp lệ
        if ($product->category && !empty($product->category->slug)) {
            $breadcrumbs['items'][] = [
                'name' => $product->category->name,
                'url' => route('products.category', $product->category->slug),
                'active' => false
            ];
        }

        // Thêm tên sản phẩm vào breadcrumb
        $breadcrumbs['items'][] = [
            'name' => $product->name, 
            'url' => null, 
            'active' => true
        ];

        return view('templates.auvista.products.products-detail', compact(
            'product',
            'reviews',
            'reviewsCount',
            'averageRating',
            'distribution',
            'related_products', // Đổi tên biến ở đây
            'breadcrumbs'
        ));
    }

    /**
     * Tạo dữ liệu đánh giá mẫu cho sản phẩm
     */
    private function generateSampleReviews($product)
    {
        // Kiểm tra xem sản phẩm đã có đánh giá chưa
        if ($product->reviews()->count() > 0) {
            return $product;
        }

        $reviewsCount = rand(3, 8);
        $totalRating = 0;

        // Định nghĩa mẫu đánh giá
        $sampleReviewers = [
            ['name' => 'Nguyễn Văn A', 'email' => '<EMAIL>'],
            ['name' => 'Trần Thị B', 'email' => '<EMAIL>'],
            ['name' => 'Lê Văn C', 'email' => '<EMAIL>'],
            ['name' => 'Phạm Thị D', 'email' => '<EMAIL>'],
            ['name' => 'Hoàng Thị D', 'email' => '<EMAIL>'],
        ];

        $sampleComments = [
            'Sản phẩm rất tốt, đúng như mô tả. Tôi đã sử dụng được 2 tuần và thấy hiệu quả rõ rệt.',
            'Sản phẩm chất lượng tốt, giao hàng nhanh. Nhưng giá hơi cao so với thị trường.',
            'Tuyệt vời! Sẽ mua lại lần sau. Thương hiệu auvista luôn là sự lựa chọn hàng đầu của tôi.',
            'Sản phẩm đúng như mong đợi, rất hài lòng với chất lượng.',
            'Mẫu mã đẹp, chất lượng tốt, giá cả hợp lý. Sẽ giới thiệu cho bạn bè.',
        ];

        $ratingDistribution = [
            5 => 0,
            4 => 0,
            3 => 0,
            2 => 0,
            1 => 0
        ];

        // Tạo đánh giá mẫu
        for ($i = 0; $i < $reviewsCount; $i++) {
            // 70% là 4-5 sao, 20% là 3 sao, 10% là 1-2 sao
            $weights = [1 => 5, 2 => 5, 3 => 20, 4 => 35, 5 => 35];
            $rating = $this->getWeightedRandom($weights);
            $totalRating += $rating;
            $ratingDistribution[$rating]++;

            $reviewer = $sampleReviewers[rand(0, count($sampleReviewers) - 1)];
            $comment = $sampleComments[rand(0, count($sampleComments) - 1)];
            $createdAt = now()->subDays(rand(1, 30))->format('Y-m-d H:i:s');

            // Tạo đánh giá mới trong bảng product_reviews
            $product->reviews()->create([
                'reviewer_name' => $reviewer['name'],
                'reviewer_email' => $reviewer['email'],
                'rating' => $rating,
                'comment' => $comment,
                'recommend' => rand(0, 100) < 70, // 70% khả năng đề xuất
                'verified_purchase' => rand(0, 100) < 80, // 80% là khách hàng đã mua
                'helpful_count' => rand(0, 10),
                'status' => true,
                'created_at' => $createdAt,
                'updated_at' => $createdAt,
            ]);
        }

        // Cập nhật thống kê đánh giá
        $product->updateRatingStats();

        return $product;
    }

    /**
     * Hiển thị trang danh mục sản phẩm tổng quát
     */
    public function categories($page, Request $request)
    {
        if ($page->lang !== App::getLocale()) {
            $code = TranslationHelper::getTranslatedPageCode($page->id, 'category');
            $page = TranslationHelper::getTranslatedPage($code, App::getLocale(), 'category');
            return redirect()->route('static.page', $page->slug);
        }

        $seoData = $this->getDataSeo([
            'seo_title' => $page->seo_title ?? $page->title,
            'seo_description' => $page->seo_description ?? $page->excerpt,
            'seo_keywords' => $page->seo_keywords ?? '',
            'seo_author' => $page->author->name ?? 'AuVista',
            'seo_canonical' => route('static.page', $page->slug),
            'seo_image' => $page->image ?? ''
        ]);

        // Lấy tất cả danh mục sản phẩm (bao gồm cả cha và con)
        $categories = Category::where('type', 'product')
            ->where('status', 'active')
            ->withCount('products')
            ->orderBy('parent_id')
            ->orderBy('name')
            ->get();

        // Debug: uncomment to check data
        // Log::info('Categories count: ' . $categories->count());
        // Log::info('Request category filter: ' . $request->category);

        // Lấy tất cả thương hiệu
        $brands = Brand::withCount('products')
            ->orderBy('name')
            ->get();

        // Query sản phẩm với filters
        $query = Product::with(['category', 'brand'])
            ->where('status', true);

        // Filter by category
        $categoryFilter = $request->category;
        if ($categoryFilter && !empty($categoryFilter)) {
            // Nếu là string (từ URL param), chuyển thành array
            if (is_string($categoryFilter)) {
                $categoryFilter = [$categoryFilter];
            }
            $query->whereIn('category_id', $categoryFilter);
        }

        // Filter by brand
        if ($request->has('brand') && !empty($request->brand)) {
            $query->whereIn('brand_id', $request->brand);
        }

        // Filter by price range
        if ($request->has('price') && !empty($request->price)) {
            $priceRange = $request->price;
            switch ($priceRange) {
                case '20000000+':
                    $query->where('price', '>', 20000000);
                    break;
                case '10000000-20000000':
                    $query->whereBetween('price', [10000000, 20000000]);
                    break;
                case '5000000+':
                    $query->where('price', '>', 5000000);
                    break;
                case '2000000-5000000':
                    $query->whereBetween('price', [2000000, 5000000]);
                    break;
                case '0-2000000':
                    $query->where('price', '<', 2000000);
                    break;
            }
        }

        // Filter by rating
        if ($request->has('rating') && !empty($request->rating)) {
            $ratings = $request->rating;
            $query->whereHas('reviews', function ($q) use ($ratings) {
                $q->whereIn('rating', $ratings);
            });
        }

        // Sorting
        switch ($request->get('sort', 'default')) {
            case 'popularity':
                $query->orderByDesc('views');
                break;
            case 'best-seller':
                $query->orderByDesc('sold_count');
                break;
            case 'rating':
                $query->orderByDesc('rating');
                break;
            case 'price-asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price-desc':
                $query->orderBy('price', 'desc');
                break;
            default:
                $query->latest();
        }

        $products = $query->paginate(12)->withQueryString();
        $productsCount = $query->count();

        // Lấy tin tức liên quan
        $relatedPosts = \App\Models\Post::with('categories')
            ->where('status', true)
            ->latest()
            ->limit(6)
            ->get();

        // Truyền thêm filter parameters để view hiển thị trạng thái selected
        $selectedCategories = is_string($request->category) ? [$request->category] : ($request->category ?: []);
        $selectedBrands = $request->brand ?: [];
        $selectedPrice = $request->price;
        $selectedRating = $request->rating ?: [];

        // Tạo breadcrumbs
        $breadcrumbs = [
            'items' => [
                [
                    'name' => __('messages.home'),
                    'url' => route('home'),
                    'active' => false
                ],
                [
                    'name' => $page->title,
                    'url' => null,
                    'active' => true
                ]
            ],
            'current' => $page->title
        ];

        return view('templates.auvista.pages.products', compact(
            'page',
            'products',
            'categories',
            'brands',
            'productsCount',
            'relatedPosts',
            'selectedCategories',
            'selectedBrands',
            'selectedPrice',
            'selectedRating',
            'breadcrumbs'
        ) + $seoData);
    }

    /**
     * Hiển thị sản phẩm theo danh mục
     */
    public function category($slug, Request $request)
    {
        // Tìm category theo slug
        $category = Category::where('slug', $slug)
            ->where('type', 'product')
            ->where('status', 'active')
            ->firstOrFail();

        // Lấy tất cả danh mục sản phẩm để hiển thị trong slider
        $categories = Category::where('type', 'product')
            ->where('status', 'active')
            ->orderBy('parent_id')
            ->orderBy('name')
            ->get();

        // Lấy tất cả thương hiệu có sản phẩm trong danh mục này
        $brands = Brand::whereHas('products', function($query) use ($category) {
            $query->where('category_id', $category->id)
                  ->where('status', true);
        })->orderBy('name')->get();

        // Query sản phẩm với filters
        $query = Product::with(['category', 'brand'])
            ->where('category_id', $category->id)
            ->where('status', true);

        // Filter by brand
        if ($request->has('brand') && !empty($request->brand)) {
            $query->whereIn('brand_id', $request->brand);
        }

        // Filter by price range
        if ($request->has('price') && !empty($request->price)) {
            $priceRange = $request->price;
            switch ($priceRange) {
                case '20000000+':
                    $query->where('price', '>', 20000000);
                    break;
                case '10000000-20000000':
                    $query->whereBetween('price', [10000000, 20000000]);
                    break;
                case '5000000+':
                    $query->where('price', '>', 5000000);
                    break;
                case '2000000-5000000':
                    $query->whereBetween('price', [2000000, 5000000]);
                    break;
                case '0-2000000':
                    $query->where('price', '<', 2000000);
                    break;
            }
        }

        // Sorting
        switch ($request->get('sort', 'default')) {
            case 'popularity':
                $query->orderByDesc('views');
                break;
            case 'best-seller':
                $query->orderByDesc('sold_count');
                break;
            case 'rating':
                $query->orderByDesc('average_rating');
                break;
            case 'price-asc':
                $query->orderBy('price', 'asc');
                break;
            case 'price-desc':
                $query->orderBy('price', 'desc');
                break;
            default:
                $query->latest();
        }

        $products = $query->paginate(12)->withQueryString();

        // Truyền thêm filter parameters để view hiển thị trạng thái selected
        $selectedBrands = $request->brand ?: [];
        $selectedPrice = $request->price;

        $breadcrumbs = [
            ['title' => '<i class="fas fa-home"></i><span class="ms-1">Trang chủ</span>', 'url' => route('home')],
            ['title' => 'Sản phẩm', 'url' => route('products.index')],
            ['title' => $category->name, 'url' => route('products.category', $category->slug)]
        ];

        // Tạo object $page giả để tương thích với view template
        $page = (object) [
            'slug' => $category->slug,
            'title' => $category->name,
            'id' => $category->id
        ];

        // Thêm selected categories từ category hiện tại
        $selectedCategories = [$category->id];
        $productsCount = $query->count();

        // Lấy tin tức liên quan
        $relatedPosts = \App\Models\Post::with('categories')
            ->where('status', true)
            ->latest()
            ->limit(6)
            ->get();

        // Tạo SEO data
        $seoData = [
            'seo_title' => $category->seo_title ?? $category->name,
            'seo_description' => $category->seo_description ?? 'Khám phá các sản phẩm ' . $category->name . ' chất lượng cao tại Auvista',
            'seo_keywords' => $category->seo_keywords ?? $category->name . ', sản phẩm, auvista',
        ];

        return view('templates.auvista.pages.products', compact(
            'page',
            'products',
            'categories',
            'brands',
            'breadcrumbs',
            'productsCount',
            'relatedPosts',
            'selectedCategories',
            'selectedBrands',
            'selectedPrice'
        ) + $seoData);
    }

    /**
     * Hiển thị sản phẩm theo thương hiệu
     */
    public function brand($slug)
    {
        $brand = Brand::where('slug', $slug)->firstOrFail();

        // Lấy sản phẩm của thương hiệu - lấy nhiều hơn để hiển thị
        $products = Product::with(['category'])
            ->where('brand_id', $brand->id)
            ->where('status', true)
            ->latest()
            ->get(); // Lấy tất cả thay vì paginate để dễ xử lý trong view

        $seoData = $this->getDataSeo([
            'title' => 'Thương hiệu ' . $brand->name . ' - AVPlus',
            'meta_description' => $brand->description ?: 'Khám phá các sản phẩm chất lượng từ thương hiệu ' . $brand->name,
            'meta_keywords' => 'thương hiệu, ' . $brand->name . ', sản phẩm âm thanh'
        ]);

        $breadcrumbs = [
            ['title' => '<i class="fas fa-home"></i><span class="ms-1">Trang chủ</span>', 'url' => route('home')],
            ['title' => 'Sản phẩm', 'url' => route('products.index')],
            ['title' => $brand->name, 'url' => route('products.brand', $brand->slug)]
        ];

        // Kiểm tra nếu là các thương hiệu đặc biệt thì sử dụng view riêng
        $specialBrands = ['nutrilite', 'artistry', 'bodykey'];

        if (in_array($slug, $specialBrands)) {
            // Lấy 6 sản phẩm nổi bật từ thương hiệu
            $featuredProducts = Product::with(['category'])
                ->where('brand_id', $brand->id)
                ->where('status', true)
                ->limit(6)
                ->get();

            return view("templates.auvista.brands.{$slug}", compact('brand', 'products', 'featuredProducts', 'breadcrumbs'));
        }

        // Sử dụng view brand-detail mới
        return view('templates.auvista.pages.brand-detail', compact('brand', 'products', 'breadcrumbs') + $seoData);
    }

    /**
     * Tìm kiếm sản phẩm
     */
    public function search(Request $request)
    {
        $query = $request->get('q');

        $products = Product::with(['category', 'brand'])
            ->where('status', true)
            ->where(function ($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                    ->orWhere('description', 'like', "%{$query}%");
            })
            ->latest()
            ->paginate(12);

        $breadcrumbs = [
            ['title' => '<i class="fas fa-home"></i><span class="ms-1">Trang chủ</span>', 'url' => route('home')],
            ['title' => 'Sản phẩm', 'url' => route('products.index')],
            ['title' => 'Tìm kiếm: ' . $query, 'url' => route('products.search') . '?q=' . urlencode($query)]
        ];

        return view('templates.auvista.products.search', compact('products', 'query', 'breadcrumbs'));
    }

    /**
     * Hiển thị sản phẩm dinh dưỡng sức khỏe
     */
    public function nutritionHealth()
    {
        $categorySlug = 'dinh-duong-suc-khoe';
        $category = Category::where('slug', $categorySlug)->firstOrFail();

        $products = Product::with(['brand'])
            ->where('category_id', $category->id)
            ->where('status', true)
            ->latest()
            ->paginate(12);

        $breadcrumbs = [
            ['title' => '<i class="fas fa-home"></i><span class="ms-1">Trang chủ</span>', 'url' => route('home')],
            ['title' => 'Sản phẩm', 'url' => route('products.index')],
            ['title' => $category->name, 'url' => route('products.category', $category->slug)]
        ];

        $categories = Category::where('parent_id', null)->get();

        $seoData = $this->getDataSeo([
            'title' => 'Sản phẩm Dinh dưỡng & Sức khỏe - Midsun Việt Nam',
            'meta_description' => 'Khám phá các sản phẩm dinh dưỡng và sức khỏe chất lượng cao của Midsun Việt Nam',
            'meta_keywords' => 'dinh dưỡng, sức khỏe, vitamin, thực phẩm chức năng'
        ]);

        return view('templates.auvista.products.category-products', compact('category', 'products', 'breadcrumbs', 'categories') + $seoData);
    }

    /**
     * Hiển thị sản phẩm chăm sóc da
     */
    public function skinCare()
    {
        $categorySlug = 'cham-soc-da';
        $category = Category::where('slug', $categorySlug)->firstOrFail();

        $products = Product::with(['brand'])
            ->where('category_id', $category->id)
            ->where('status', true)
            ->latest()
            ->paginate(12);

        $breadcrumbs = [
            ['title' => '<i class="fas fa-home"></i><span class="ms-1">Trang chủ</span>', 'url' => route('home')],
            ['title' => 'Sản phẩm', 'url' => route('products.index')],
            ['title' => $category->name, 'url' => route('products.category', $category->slug)]
        ];

        $categories = Category::where('parent_id', null)->get();

        $seoData = $this->getDataSeo([
            'title' => 'Sản phẩm Chăm sóc da - Midsun Việt Nam',
            'meta_description' => 'Khám phá các sản phẩm chăm sóc da chất lượng cao của Midsun Việt Nam',
            'meta_keywords' => 'chăm sóc da, mỹ phẩm, dưỡng da'
        ]);

        return view('templates.auvista.products.category-products', compact('category', 'products', 'breadcrumbs', 'categories') + $seoData);
    }

    /**
     * Hiển thị sản phẩm trang điểm
     */
    public function makeup()
    {
        $categorySlug = 'trang-diem';
        $category = Category::where('slug', $categorySlug)->firstOrFail();

        $products = Product::with(['brand'])
            ->where('category_id', $category->id)
            ->where('status', true)
            ->latest()
            ->paginate(12);

        $breadcrumbs = [
            ['title' => '<i class="fas fa-home"></i><span class="ms-1">Trang chủ</span>', 'url' => route('home')],
            ['title' => 'Sản phẩm', 'url' => route('products.index')],
            ['title' => $category->name, 'url' => route('products.category', $category->slug)]
        ];

        $categories = Category::where('parent_id', null)->get();

        $seoData = $this->getDataSeo([
            'title' => 'Sản phẩm Trang điểm - Midsun Việt Nam',
            'meta_description' => 'Khám phá các sản phẩm trang điểm chất lượng cao của Midsun Việt Nam',
            'meta_keywords' => 'trang điểm, makeup, mỹ phẩm'
        ]);

        return view('templates.auvista.products.category-products', compact('category', 'products', 'breadcrumbs', 'categories') + $seoData);
    }

    /**
     * Hiển thị sản phẩm chăm sóc cá nhân
     */
    public function personalCare()
    {
        $categorySlug = 'cham-soc-ca-nhan';
        $category = Category::where('slug', $categorySlug)->firstOrFail();

        $products = Product::with(['brand'])
            ->where('category_id', $category->id)
            ->where('status', true)
            ->latest()
            ->paginate(12);

        $breadcrumbs = [
            ['title' => '<i class="fas fa-home"></i><span class="ms-1">Trang chủ</span>', 'url' => route('home')],
            ['title' => 'Sản phẩm', 'url' => route('products.index')],
            ['title' => $category->name, 'url' => route('products.category', $category->slug)]
        ];

        $categories = Category::where('parent_id', null)->get();

        $seoData = $this->getDataSeo([
            'title' => 'Sản phẩm Chăm sóc cá nhân - Midsun Việt Nam',
            'meta_description' => 'Khám phá các sản phẩm chăm sóc cá nhân chất lượng cao của Midsun Việt Nam',
            'meta_keywords' => 'chăm sóc cá nhân, sức khỏe, làm đẹp'
        ]);

        return view('templates.auvista.products.category-products', compact('category', 'products', 'breadcrumbs', 'categories') + $seoData);
    }

    /**
     * Tạo số ngẫu nhiên có trọng số
     * @param array $weights Mảng trọng số [giá_trị => trọng_số]
     * @return int|string Giá trị được chọn
     */
    private function getWeightedRandom(array $weights)
    {
        $totalWeight = array_sum($weights);
        $randomNumber = mt_rand(1, $totalWeight);

        $weightSum = 0;
        foreach ($weights as $value => $weight) {
            $weightSum += $weight;
            if ($randomNumber <= $weightSum) {
                return $value;
            }
        }

        // Fallback (không nên xảy ra)
        return array_key_first($weights);
    }
}
