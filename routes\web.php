<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\PostController;
use App\Http\Controllers\StaticPageController;
use App\Http\Controllers\ContactController;
use App\Http\Controllers\TestController;
use App\Http\Controllers\ProductController;

use App\Http\Controllers\AuthController;
use App\Http\Controllers\Auth\ForgotPasswordController;
use App\Http\Controllers\Auth\ResetPasswordController;
use App\Http\Controllers\ProfileController;
use App\Http\Controllers\OrderController;
use App\Http\Controllers\CategoryProductController;
use App\Http\Controllers\CartController;
use App\Livewire\CartPage;
use App\Http\Controllers\WishlistController;
use App\Http\Controllers\NewsletterController;
use App\Http\Controllers\ProductReviewController;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Session;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
*/
// Route đổi ngôn ngữ
Route::get('lang/{lang}', function ($lang) {
    // Sử dụng LanguageSyncService để đồng bộ ngôn ngữ
    \App\Services\LanguageSyncService::syncLocale($lang, 'route');

    // Chuyển hướng về trang trước đó
    return redirect()->back();
})->name('lang');

// Trang chủ
Route::get('/', [HomeController::class, 'index'])->name('home');

// Routes cho bài viết
Route::get('/bai-viet/{slug}.html', [PostController::class, 'detail'])->name('post.detail');
Route::get('/search', [PostController::class, 'search'])->name('post.search');

// Routes cho Contact Form
Route::post('/lien-he', [ContactController::class, 'store'])->name('contact.send');

// Product routes - Đặt lên trước route chung
Route::get('/san-pham', [ProductController::class, 'index'])->name('products.index');
Route::prefix('san-pham')->group(function () {
    Route::get('/tim-kiem', [ProductController::class, 'search'])->name('products.search');
    Route::get('/{slug}', [ProductController::class, 'show'])->name('products.show');
});

// Route cho danh mục sản phẩm
Route::get('/danh-muc-san-pham/{slug}', [ProductController::class, 'category'])->name('products.category');

// Route cho thương hiệu - đặt trước route pattern chung
Route::get('/thuong-hieu', [StaticPageController::class, 'brand'])->name('page.brand');
Route::get('/thuong-hieu/{slug}', action: [StaticPageController::class, 'brandDetail'])->name('products.brand');

// Route chung cho chi tiết trang tĩnh và bài viết
Route::get('/{slug}.html', function ($slug) {
    // Kiểm tra tồn tại của trang tĩnh trước
    $staticPage = \App\Models\StaticPage::where('slug', $slug)
        ->where('lang', \Illuminate\Support\Facades\App::getLocale())
        ->where('status', 'published')
        ->first();

    if ($staticPage) {
        // Nếu tồn tại StaticPage thì hiển thị chi tiết trang tĩnh
        return app(\App\Http\Controllers\StaticPageController::class)->detail($staticPage);
    } else if ($slug === 'gioi-thieu') {
        // Xử lý riêng cho trang giới thiệu
        return app(\App\Http\Controllers\StaticPageController::class)->about();
    } else {
        // Hiển thị chi tiết bài viết thường
        return app(\App\Http\Controllers\PostController::class)->detail($slug);
    }
})->name('link.detail')
    ->where('slug', '^(?!admin|api|san-pham|bai-viet|thuong-hieu|giai-phap).*$'); // Loại trừ giai-phap

// Các trang tĩnh riêng biệt
Route::get('/gioi-thieu', [StaticPageController::class, 'about'])->name('page.about');
Route::get('/giai-phap', [StaticPageController::class, 'solutions'])->name('page.solutions');

// Order routes
Route::middleware('auth:frontend')->group(function () {
    Route::get('/checkout', function () {
        return view('templates.auvista.pages.checkout');
    })->name('checkout');
});
// Cho phép khách lẻ đặt hàng, không cần đăng nhập
Route::post('/orders', [OrderController::class, 'store'])->name('orders.store');
Route::get('/orders/{order}/success', [OrderController::class, 'success'])->name('orders.success');
// Guest routes
Route::middleware('guest:frontend')->group(function () {
    // Login routes
    Route::get('/login', [AuthController::class, 'loginForm'])->name('login');
    Route::post('/login', [AuthController::class, 'login']);

    // Password Reset routes
    Route::get('/forgot-password', [ForgotPasswordController::class, 'showLinkRequestForm'])->name('password.request');
    Route::post('/password/email', [ForgotPasswordController::class, 'sendResetLinkEmail'])->name('password.email');
    Route::get('/password/reset/{token}', [ResetPasswordController::class, 'showResetForm'])->name('password.reset');
    Route::post('/password/reset', [ResetPasswordController::class, 'reset'])->name('password.update');

    // Register routes
    Route::get('/register', [AuthController::class, 'registerLanding'])->name('register.landing');
    Route::get('/register/distributor', [AuthController::class, 'registerDistributorForm'])->name('register.distributor');
    Route::post('/register/distributor', [AuthController::class, 'registerDistributor'])->name('register.distributor.submit');
    Route::get('/register/customer', [AuthController::class, 'registerCustomerForm'])->name('register.customer');
    Route::post('/register/customer', [AuthController::class, 'registerCustomer'])->name('register.customer.submit');
    Route::post('/register', [AuthController::class, 'register']);
});

Route::middleware('auth:frontend')->group(function () {
    Route::post('/logout', [AuthController::class, 'logout'])->name('logout');

    // Trang quản lý tài khoản
    Route::get('/tai-khoan', [ProfileController::class, 'edit'])->name('profile.edit');
    Route::post('/tai-khoan/cap-nhat', [ProfileController::class, 'update'])->name('profile.update');

    // Trang quản lý đơn hàng
    Route::get('/don-hang', [OrderController::class, 'index'])->name('orders.index');
    Route::get('/don-hang/{id}', [OrderController::class, 'show'])->name('orders.show');
    Route::patch('/don-hang/{id}/cancel', [OrderController::class, 'cancel'])->name('orders.cancel');
});

// Route cho xác thực email
Route::get('/email/verify', [\App\Http\Controllers\Auth\VerificationController::class, 'notice'])
    ->middleware('auth:frontend')
    ->name('verification.notice');

Route::post('/email/verification-notification', [\App\Http\Controllers\Auth\VerificationController::class, 'send'])
    ->middleware('auth:frontend')
    ->name('verification.send');

Route::get('/email/verify/{id}/{token}', [\App\Http\Controllers\Auth\VerificationController::class, 'verify'])
    ->name('verification.verify');

// Routes cho Wishlist - Đặt trước route pattern chung
Route::get('/danh-sach-yeu-thich', [WishlistController::class, 'index'])->name('wishlist');
Route::post('/wishlist/add', [WishlistController::class, 'add'])->name('wishlist.add');
Route::delete('/wishlist/remove', [WishlistController::class, 'remove'])->name('wishlist.remove');

// Route cho tìm kiếm bài viết
Route::get('/search', [PostController::class, 'search'])->name('post.search');

// Route cho chi tiết tin tức
Route::get('/tin-tuc/{slug}', [PostController::class, 'detail'])->name('news.detail');

// Route cho trang dự án
Route::get('/du-an/{categorySlug}', [PostController::class, 'projectsByCategory'])->name('du-an.category');


// Route cho trang giải pháp
Route::get('/giai-phap/{slug}', [StaticPageController::class, 'solutionDetail'])->name('solutions.detail');

// Route cho trang giỏ hàng
Route::get('/gio-hang', [CartController::class, 'index'])->name('cart');
Route::patch('/gio-hang/update/{productId}', [CartController::class, 'update'])->name('cart.update');
Route::delete('/gio-hang/remove/{productId}', [CartController::class, 'remove'])->name('cart.remove');

// AJAX Routes cho giỏ hàng
Route::middleware('web')->group(function () {
    Route::post('/api/cart/update/{productId}', [CartController::class, 'updateAjax'])->name('cart.update.ajax');
    Route::delete('/api/cart/remove/{productId}', [CartController::class, 'removeAjax'])->name('cart.remove.ajax');
});

Route::post('/gio-hang/thanh-toan', function () {
    // Xử lý thanh toán - tạm thời redirect về cart
    return redirect()->route('cart')->with('success', 'Đơn hàng đã được gửi thành công!');
})->name('cart.checkout');

// Cart routes - fallback for old cart functionality
Route::middleware('web')->post('/cart/add', function (Illuminate\Http\Request $request) {
    $productId = $request->input('product_id');
    $quantity = $request->input('quantity', 1);

    try {
        $product = \App\Models\Product::findOrFail($productId);

        // Lấy giỏ hàng hiện tại từ Session
        $cartItems = session('cart', []);

        // Thêm vào giỏ hàng
        if (isset($cartItems[$productId])) {
            $cartItems[$productId] += $quantity;
        } else {
            $cartItems[$productId] = $quantity;
        }

        // Lưu lại vào Session và force save
        session(['cart' => $cartItems]);
        session()->save(); // Force save session

        \Illuminate\Support\Facades\Log::info('Cart add via route:', [
            'productId' => $productId,
            'quantity' => $quantity,
            'cart' => $cartItems,
            'session_id' => session()->getId()
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Sản phẩm đã được thêm vào giỏ hàng',
            'cartCount' => count($cartItems)
        ]);
    } catch (\Exception $e) {
        \Illuminate\Support\Facades\Log::error('Cart add error:', [
            'error' => $e->getMessage(),
            'productId' => $productId
        ]);

        return response()->json([
            'success' => false,
            'message' => 'Không thể thêm sản phẩm vào giỏ hàng: ' . $e->getMessage()
        ], 500);
    }
})->name('cart.add');

// Route cho danh mục bài viết - đặt ở cuối cùng của các route frontend
Route::get('/{slug}/', [PostController::class, 'category'])->name('category.posts')
    ->where('slug', '^(?!admin|api|cart).*$');

// Admin API routes
Route::prefix('admin/api')->middleware(['web', 'auth'])->group(function () {
    // Endpoint để đặt danh mục chính cho bài viết
    Route::post('/posts/{post}/set-primary-category/{category}', function (\App\Models\Post $post, $category) {
        try {
            // Kiểm tra xem category có thuộc về post không
            $hasCategory = $post->categories()->where('category_id', $category)->exists();

            if (!$hasCategory) {
                return response()->json([
                    'success' => false,
                    'message' => 'Danh mục không thuộc về bài viết này.'
                ], 400);
            }

            // Đặt danh mục chính
            \Illuminate\Support\Facades\DB::table('post_category')
                ->where('post_id', $post->id)
                ->update(['is_primary' => false]);

            \Illuminate\Support\Facades\DB::table('post_category')
                ->where('post_id', $post->id)
                ->where('category_id', $category)
                ->update(['is_primary' => true]);

            return response()->json([
                'success' => true,
                'message' => 'Đã đặt danh mục chính.'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    });
});

// Admin Category Products
Route::prefix('admin')->name('admin.')->middleware(['auth'])->group(function () {
    Route::resource('category-products', \App\Http\Controllers\CategoryProductController::class);
});

// Newsletter route
Route::post('/newsletter/subscribe', [\App\Http\Controllers\NewsletterController::class, 'subscribe'])->name('newsletter.subscribe');

// Product review routes
Route::post('/products/{product}/reviews', [\App\Http\Controllers\ProductReviewController::class, 'store'])->name('products.reviews.store');
Route::post('/reviews/{review}/helpful', [\App\Http\Controllers\ProductReviewController::class, 'markHelpful'])->name('reviews.helpful');

// Routes cho quản lý đánh giá - admin
Route::middleware(['auth'])->prefix('admin')->name('admin.')->group(function () {
    Route::get('/reviews', [\App\Http\Controllers\ProductReviewController::class, 'allReviews'])->name('reviews.index');
    Route::get('/reviews/pending', [\App\Http\Controllers\ProductReviewController::class, 'pendingReviews'])->name('reviews.pending');
    Route::post('/reviews/{review}/reply', [\App\Http\Controllers\ProductReviewController::class, 'adminReply'])->name('reviews.reply');
    Route::post('/reviews/{review}/status', [\App\Http\Controllers\ProductReviewController::class, 'updateStatus'])->name('reviews.status');
});

Route::middleware(['auth', 'verified'])->group(function () {
    Route::post('/reviews/{review}/status', [\App\Http\Controllers\ProductReviewController::class, 'updateStatus'])->name('reviews.status');
});

// Trang tĩnh
Route::get('/{slug}', function ($slug) {
    // Debug logging
    \Illuminate\Support\Facades\Log::info("Route /{slug} được gọi với slug: " . $slug);
    
    $check = \App\Models\StaticPage::where('slug', $slug)->first();

    if ($check) {
        \Illuminate\Support\Facades\Log::info("Tìm thấy StaticPage: " . $check->title);
        return (new StaticPageController)->detail($check);
    }
    
    // Kiểm tra xem có phải bài viết không trước khi check category
    $post = \App\Models\Post::where('slug', $slug)
        ->where('status', 'published')
        ->where('lang', \Illuminate\Support\Facades\App::getLocale())
        ->first();
        
    \Illuminate\Support\Facades\Log::info("Kiểm tra Post với slug: " . $slug . " - " . ($post ? "Tìm thấy: " . $post->title : "Không tìm thấy"));
        
    if ($post) {
        // TEMPORARY: Force tất cả bài viết dùng solution template để test
        \Illuminate\Support\Facades\Log::info("Chuyển đến solutionDetail cho post: " . $post->title);
        return app(\App\Http\Controllers\StaticPageController::class)->solutionDetail($slug);
        
        // Kiểm tra xem có thuộc category solution/hội nghị không
        $isSolution = $post->categories()->where(function($query) {
            $query->where('slug', 'hoi-nghi-truyen-hinh')
                  ->orWhere('slug', 'giai-phap')
                  ->orWhere('name', 'like', '%hội nghị%')
                  ->orWhere('name', 'like', '%giải pháp%');
        })->exists();
        
        if ($isSolution) {
            return app(\App\Http\Controllers\StaticPageController::class)->solutionDetail($slug);
        } else {
            return app(\App\Http\Controllers\PostController::class)->detail($slug);
        }
    }
    
    $check = \App\Models\Category::where('slug', $slug)->first();

    if ($check) {
        \Illuminate\Support\Facades\Log::info("Tìm thấy Category: " . $check->name . " (type: " . $check->type . ")");
        
        // Kiểm tra xem có phải category hội nghị/giải pháp không
        $isSolutionCategory = (
            $check->slug == 'hoi-nghi-truyen-hinh' ||
            $check->slug == 'giai-phap' ||
            stripos($check->name, 'hội nghị') !== false ||
            stripos($check->name, 'giải pháp') !== false
        );
        
        if ($isSolutionCategory) {
            \Illuminate\Support\Facades\Log::info("Category là solution category, chuyển đến solution template");
            // Lấy bài viết đầu tiên trong category này sử dụng relationship đúng cách
            $firstPost = \App\Models\Post::whereHas('categories', function($query) use ($check) {
                $query->where('categories.id', $check->id);
            })->where('status', 'published')->first();
            
            if ($firstPost) {
                \Illuminate\Support\Facades\Log::info("Tìm thấy bài viết đầu tiên: " . $firstPost->title);
                return app(\App\Http\Controllers\StaticPageController::class)->solutionDetail($firstPost->slug);
            } else {
                // Nếu không có bài viết, hiển thị category page với template solution
                \Illuminate\Support\Facades\Log::info("Không có bài viết, hiển thị fallback");
                return view('templates.auvista.pages.solution-detail', [
                    'solution' => (object)[
                        'title' => $check->name,
                        'content' => $check->description ?? 'Nội dung đang được cập nhật...',
                        'excerpt' => $check->description ?? '',
                        'created_at' => now(),
                        'published_at' => now(),
                        'categories' => collect([$check]),
                        'category' => $check, // Template expects this singular property
                        'image' => null,
                        'featured_image' => null,
                        'author' => null,
                        'views_count' => 0
                    ],
                    'featuredProducts' => collect(),
                    'relatedSolutions' => collect()
                ]);
            }
        }
        
        if ($check->type == 'product') {
            \Illuminate\Support\Facades\Log::info("Chuyển đến ProductController->category");
            return (new ProductController)->category($check->slug, request());
        } else {
            \Illuminate\Support\Facades\Log::info("Chuyển đến PostController->category");
            return (new PostController)->category($slug);
        }
    }
    
    \Illuminate\Support\Facades\Log::info("Không tìm thấy gì, redirect về home");
    return redirect()->route('home');
})->name('static.page');
