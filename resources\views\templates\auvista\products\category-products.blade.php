@extends('templates.auvista.layouts.default')

@section('title', $category->seo_title ?? $category->name . ' - Auvista')
@section('meta_description', $category->seo_description ?? 'Khám phá các sản phẩm ' . $category->name . ' chất lượng cao tại Auvista')

@section('content')
    <!-- Main Content -->
    <main id="main" id="site-main">
        <div class="page-wrapper blog-archive mb-7 lg:mb-[54px] mt-12 lg:mt-[70px]">
            <div class="container mx-auto">
                <!-- Breadcrumbs -->
                <nav class="flex mb-6" aria-label="Breadcrumb">
                    <ol class="inline-flex items-center space-x-1 md:space-x-3">
                        @if(isset($breadcrumbs))
                            @foreach($breadcrumbs as $breadcrumb)
                                <li class="inline-flex items-center">
                                    @if(!$loop->last)
                                        <a href="{{ $breadcrumb['url'] }}" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-blue-600">
                                            {!! $breadcrumb['title'] !!}
                                        </a>
                                        <svg class="w-6 h-6 text-gray-400 mx-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    @else
                                        <span class="ml-1 text-sm font-medium text-gray-500 md:ml-2">{!! $breadcrumb['title'] !!}</span>
                                    @endif
                                </li>
                            @endforeach
                        @endif
                    </ol>
                </nav>

                <!-- Category Header -->
                <div class="wrapper-container pb-8 border-b border-primary-border">
                    <div class="flex items-center gap-6 mb-6">
                        @if(isset($category->thumbnail) && $category->thumbnail)
                            <div class="w-20 h-20 rounded-xl overflow-hidden flex-shrink-0">
                                <img src="{{ asset('storage/' . $category->thumbnail) }}"
                                     alt="{{ $category->name }}"
                                     class="w-full h-full object-cover">
                            </div>
                        @endif
                        <div>
                            <h1 class="text-2xl lg:text-[38px]/[1.4] font-bold text-primary-base mb-2">
                                {{ $category->name }}
                            </h1>
                            @if(isset($category->description) && $category->description)
                                <p class="text-primary-gray2 text-lg">{{ $category->description }}</p>
                            @endif
                        </div>
                    </div>

                    <!-- Related Categories -->
                    @if(isset($categories) && $categories->count() > 1)
                        <div class="categories-swiper swiper-slider relative -ml-2 -mr-2"
                             data-items="5" data-mobile="2" data-tablet="3" data-desktop="4"
                             data-large="5" data-xlarge="6" data-spacing="0" data-loop="false" data-navigation="true">
                            <div class="swiper">
                                <div class="swiper-wrapper">
                                    <!-- "Tất cả" option -->
                                    <div class="swiper-slide px-2">
                                        <div class="">
                                            <a href="{{ route('products.index') }}"
                                               class="block overflow-hidden aspect-[1.77419355] rounded-xl">
                                                <img src="{{ asset('images/img-cat-1.png') }}"
                                                     alt="Tất cả danh mục"
                                                     class="w-full h-full object-cover hover:scale-110 duration-500" />
                                            </a>
                                            <div class="pt-3">
                                                <a href="{{ route('products.index') }}"
                                                   class="text-primary-gray2 font-medium hover:text-secondary-main transition-colors">
                                                    Tất cả danh mục
                                                </a>
                                            </div>
                                        </div>
                                    </div>

                                    @foreach($categories as $cat)
                                        <div class="swiper-slide px-2">
                                            <div class="">
                                                <a href="{{ route('products.category', $cat->slug) }}"
                                                   class="block overflow-hidden aspect-[1.77419355] rounded-xl">
                                                    <img src="{{ getImageUrl($cat->thumbnail ?? '', 'images/img-cat-' . (($loop->index % 6) + 1) . '.png') }}"
                                                         alt="{{ $cat->name }}"
                                                         class="w-full h-full object-cover hover:scale-110 duration-500" />
                                                </a>
                                                <div class="pt-3">
                                                    <a href="{{ route('products.category', $cat->slug) }}"
                                                       class="text-primary-gray2 font-medium hover:text-secondary-main transition-colors {{ $cat->id == $category->id ? 'text-secondary-main font-bold' : '' }}">
                                                        {{ $cat->name }}
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                            <!-- Navigation buttons -->
                            <div class="swiper-button-next shadow-new-shadow bg-primary-background3 top-[35%] xl:-right-4"></div>
                            <div class="swiper-button-prev shadow-new-shadow bg-primary-background3 top-[35%] xl:-left-4"></div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Products Section -->
            <div class="container mx-auto mt-8 mb-8">
                <div class="flex items-start justify-between flex-col lg:flex-row gap-[30px]">
                    <!-- Products Grid -->
                    <div class="w-full order-2">
                        <div class="flex justify-between items-center mb-6 lg:mb-8">
                            <span class="font-medium">
                                {{ isset($products) ? $products->total() : 0 }} sản phẩm trong danh mục "{{ $category->name }}"
                            </span>
                            <div class="flex items-center gap-4">
                                <span class="text-primary-gray2">Sắp xếp theo</span>
                                <form method="GET" action="{{ route('products.category', $category->slug) }}" class="inline">
                                    <select name="sort" onchange="this.form.submit()" class="bg-white border border-primary-border rounded-full text-primary-gray2 focus:outline-none focus:ring-0 focus:border-secondary-main">
                                        <option value="default" {{ request('sort') == 'default' ? 'selected' : '' }}>Mặc định</option>
                                        <option value="popularity" {{ request('sort') == 'popularity' ? 'selected' : '' }}>Thứ tự mức độ phổ biến</option>
                                        <option value="best-seller" {{ request('sort') == 'best-seller' ? 'selected' : '' }}>Bán chạy nhất</option>
                                        <option value="rating" {{ request('sort') == 'rating' ? 'selected' : '' }}>Điểm đánh giá</option>
                                        <option value="price-asc" {{ request('sort') == 'price-asc' ? 'selected' : '' }}>Giá thấp đến cao</option>
                                        <option value="price-desc" {{ request('sort') == 'price-desc' ? 'selected' : '' }}>Giá cao đến thấp</option>
                                    </select>
                                </form>
                            </div>
                        </div>

                        <div class="products list-products grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 lg:gap-5 xl:gap-[30px]">
                            @if(isset($products) && $products->count() > 0)
                                @foreach($products as $product)
                                    @include('templates.auvista.components.product-card', ['product' => $product])
                                @endforeach
                            @else
                                <div class="col-span-full text-center py-12">
                                    <div class="max-w-md mx-auto">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2 2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">Không có sản phẩm</h3>
                                        <p class="mt-1 text-sm text-gray-500">Hiện tại chưa có sản phẩm nào trong danh mục "{{ $category->name }}".</p>
                                        <div class="mt-6">
                                            <a href="{{ route('products.index') }}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-gradient2 hover:bg-gradient2-hover">
                                                Xem tất cả sản phẩm
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <!-- Pagination -->
                        @if(isset($products) && $products->hasPages())
                            <div class="mt-8 flex justify-center">
                                {{ $products->links() }}
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <!-- Category Description Section -->
        @if(isset($category->description) && $category->description)
            <div class="bg-primary-grey pt-12 pb-12">
                <div class="container mx-auto">
                    <div class="term-description">
                        <h4 class="text-2xl/[1.4] mb-4 lg:mb-8">
                            Về danh mục {{ $category->name }}
                        </h4>
                        <div class="text-base/[1.4] mb-4">
                            {!! nl2br(e($category->description)) !!}
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </main>
@endsection