{"__meta": {"id": "01K0BB65ZA9WGQYH5V03W427S0", "datetime": "2025-07-17 11:44:52", "utime": **********.587727, "method": "GET", "uri": "/admin/menu-items/5", "ip": "127.0.0.1"}, "messages": {"count": 8, "messages": [{"message": "[11:44:51] LOG.info: Menu ID set from mount method {\n    \"menu_id\": 5\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.269813, "xdebug_link": null, "collector": "log"}, {"message": "[11:44:51] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.432673, "xdebug_link": null, "collector": "log"}, {"message": "[11:44:51] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.731999, "xdebug_link": null, "collector": "log"}, {"message": "[11:44:51] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.813092, "xdebug_link": null, "collector": "log"}, {"message": "[11:44:51] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.908444, "xdebug_link": null, "collector": "log"}, {"message": "[11:44:52] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.039918, "xdebug_link": null, "collector": "log"}, {"message": "[11:44:52] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.087693, "xdebug_link": null, "collector": "log"}, {"message": "[11:44:52] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.121523, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.50408, "end": **********.587815, "duration": 2.0837349891662598, "duration_str": "2.08s", "measures": [{"label": "Booting", "start": **********.50408, "relative_start": 0, "end": **********.983943, "relative_end": **********.983943, "duration": 0.*****************, "duration_str": "480ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.983957, "relative_start": 0.****************, "end": **********.58782, "relative_end": 5.0067901611328125e-06, "duration": 1.***************, "duration_str": "1.6s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.13811, "relative_start": 0.****************, "end": **********.142209, "relative_end": **********.142209, "duration": 0.004099130630493164, "duration_str": "4.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.admin.resources.menu-item-resource.pages.manage-menu-items", "start": **********.563556, "relative_start": 1.****************, "end": **********.563556, "relative_end": **********.563556, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.574961, "relative_start": 1.****************, "end": **********.574961, "relative_end": **********.574961, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.595185, "relative_start": 1.0911049842834473, "end": **********.595185, "relative_end": **********.595185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.600561, "relative_start": 1.0964808464050293, "end": **********.600561, "relative_end": **********.600561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.601754, "relative_start": 1.0976738929748535, "end": **********.601754, "relative_end": **********.601754, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.62633, "relative_start": 1.1222498416900635, "end": **********.62633, "relative_end": **********.62633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.629006, "relative_start": 1.1249258518218994, "end": **********.629006, "relative_end": **********.629006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.631431, "relative_start": 1.1273510456085205, "end": **********.631431, "relative_end": **********.631431, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.650734, "relative_start": 1.1466538906097412, "end": **********.650734, "relative_end": **********.650734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.654075, "relative_start": 1.1499948501586914, "end": **********.654075, "relative_end": **********.654075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.677536, "relative_start": 1.1734559535980225, "end": **********.677536, "relative_end": **********.677536, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.698409, "relative_start": 1.194329023361206, "end": **********.698409, "relative_end": **********.698409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.699028, "relative_start": 1.1949479579925537, "end": **********.699028, "relative_end": **********.699028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.699539, "relative_start": 1.1954588890075684, "end": **********.699539, "relative_end": **********.699539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.714943, "relative_start": 1.2108628749847412, "end": **********.714943, "relative_end": **********.714943, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.794558, "relative_start": 1.290477991104126, "end": **********.794558, "relative_end": **********.794558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.809229, "relative_start": 1.3051488399505615, "end": **********.809229, "relative_end": **********.809229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.811074, "relative_start": 1.3069939613342285, "end": **********.811074, "relative_end": **********.811074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.811464, "relative_start": 1.3073840141296387, "end": **********.811464, "relative_end": **********.811464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.811986, "relative_start": 1.307905912399292, "end": **********.811986, "relative_end": **********.811986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.812319, "relative_start": 1.3082389831542969, "end": **********.812319, "relative_end": **********.812319, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.870688, "relative_start": 1.366607904434204, "end": **********.870688, "relative_end": **********.870688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.872109, "relative_start": 1.3680288791656494, "end": **********.872109, "relative_end": **********.872109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.875, "relative_start": 1.370919942855835, "end": **********.875, "relative_end": **********.875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.876901, "relative_start": 1.3728208541870117, "end": **********.876901, "relative_end": **********.876901, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.896455, "relative_start": 1.3923749923706055, "end": **********.896455, "relative_end": **********.896455, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.89829, "relative_start": 1.394209861755371, "end": **********.89829, "relative_end": **********.89829, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.899979, "relative_start": 1.3958990573883057, "end": **********.899979, "relative_end": **********.899979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.902692, "relative_start": 1.3986120223999023, "end": **********.902692, "relative_end": **********.902692, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.904041, "relative_start": 1.399960994720459, "end": **********.904041, "relative_end": **********.904041, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.906963, "relative_start": 1.4028830528259277, "end": **********.906963, "relative_end": **********.906963, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.020624, "relative_start": 1.5165438652038574, "end": **********.020624, "relative_end": **********.020624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.023185, "relative_start": 1.5191049575805664, "end": **********.023185, "relative_end": **********.023185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.023663, "relative_start": 1.519582986831665, "end": **********.023663, "relative_end": **********.023663, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.039044, "relative_start": 1.534963846206665, "end": **********.039044, "relative_end": **********.039044, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.084146, "relative_start": 1.5800659656524658, "end": **********.084146, "relative_end": **********.084146, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.085954, "relative_start": 1.581873893737793, "end": **********.085954, "relative_end": **********.085954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.0864, "relative_start": 1.582319974899292, "end": **********.0864, "relative_end": **********.0864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.087025, "relative_start": 1.5829448699951172, "end": **********.087025, "relative_end": **********.087025, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.120702, "relative_start": 1.616621971130371, "end": **********.120702, "relative_end": **********.120702, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.141129, "relative_start": 1.6370489597320557, "end": **********.141129, "relative_end": **********.141129, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.142636, "relative_start": 1.6385560035705566, "end": **********.142636, "relative_end": **********.142636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.143369, "relative_start": 1.6392889022827148, "end": **********.143369, "relative_end": **********.143369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.154245, "relative_start": 1.6501648426055908, "end": **********.154245, "relative_end": **********.154245, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.154933, "relative_start": 1.650852918624878, "end": **********.154933, "relative_end": **********.154933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.156753, "relative_start": 1.6526730060577393, "end": **********.156753, "relative_end": **********.156753, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.157171, "relative_start": 1.6530909538269043, "end": **********.157171, "relative_end": **********.157171, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.158532, "relative_start": 1.654451847076416, "end": **********.158532, "relative_end": **********.158532, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.169495, "relative_start": 1.6654150485992432, "end": **********.169495, "relative_end": **********.169495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.171075, "relative_start": 1.6669950485229492, "end": **********.171075, "relative_end": **********.171075, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.171422, "relative_start": 1.667341947555542, "end": **********.171422, "relative_end": **********.171422, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.172335, "relative_start": 1.6682548522949219, "end": **********.172335, "relative_end": **********.172335, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.17286, "relative_start": 1.6687798500061035, "end": **********.17286, "relative_end": **********.17286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.17332, "relative_start": 1.6692399978637695, "end": **********.17332, "relative_end": **********.17332, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.173936, "relative_start": 1.6698558330535889, "end": **********.173936, "relative_end": **********.173936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.175677, "relative_start": 1.6715970039367676, "end": **********.175677, "relative_end": **********.175677, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.176077, "relative_start": 1.671996831893921, "end": **********.176077, "relative_end": **********.176077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.177619, "relative_start": 1.6735389232635498, "end": **********.177619, "relative_end": **********.177619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.178182, "relative_start": 1.6741018295288086, "end": **********.178182, "relative_end": **********.178182, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.179806, "relative_start": 1.6757259368896484, "end": **********.179806, "relative_end": **********.179806, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.18023, "relative_start": 1.676149845123291, "end": **********.18023, "relative_end": **********.18023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.181361, "relative_start": 1.6772809028625488, "end": **********.181361, "relative_end": **********.181361, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.182006, "relative_start": 1.6779258251190186, "end": **********.182006, "relative_end": **********.182006, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.182496, "relative_start": 1.6784160137176514, "end": **********.182496, "relative_end": **********.182496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.183354, "relative_start": 1.6792738437652588, "end": **********.183354, "relative_end": **********.183354, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.18534, "relative_start": 1.6812598705291748, "end": **********.18534, "relative_end": **********.18534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.185783, "relative_start": 1.6817028522491455, "end": **********.185783, "relative_end": **********.185783, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.187456, "relative_start": 1.6833758354187012, "end": **********.187456, "relative_end": **********.187456, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.188161, "relative_start": 1.6840808391571045, "end": **********.188161, "relative_end": **********.188161, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.189802, "relative_start": 1.6857218742370605, "end": **********.189802, "relative_end": **********.189802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.190232, "relative_start": 1.6861519813537598, "end": **********.190232, "relative_end": **********.190232, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.191347, "relative_start": 1.6872668266296387, "end": **********.191347, "relative_end": **********.191347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.191953, "relative_start": 1.6878728866577148, "end": **********.191953, "relative_end": **********.191953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.192371, "relative_start": 1.6882908344268799, "end": **********.192371, "relative_end": **********.192371, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.192774, "relative_start": 1.6886940002441406, "end": **********.192774, "relative_end": **********.192774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.194507, "relative_start": 1.6904268264770508, "end": **********.194507, "relative_end": **********.194507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.195062, "relative_start": 1.6909818649291992, "end": **********.195062, "relative_end": **********.195062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.196658, "relative_start": 1.692577838897705, "end": **********.196658, "relative_end": **********.196658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.197267, "relative_start": 1.6931869983673096, "end": **********.197267, "relative_end": **********.197267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.199027, "relative_start": 1.6949470043182373, "end": **********.199027, "relative_end": **********.199027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.199524, "relative_start": 1.695443868637085, "end": **********.199524, "relative_end": **********.199524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.200602, "relative_start": 1.6965219974517822, "end": **********.200602, "relative_end": **********.200602, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.201179, "relative_start": 1.697098970413208, "end": **********.201179, "relative_end": **********.201179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.201911, "relative_start": 1.6978309154510498, "end": **********.201911, "relative_end": **********.201911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.202571, "relative_start": 1.698490858078003, "end": **********.202571, "relative_end": **********.202571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.204198, "relative_start": 1.700117826461792, "end": **********.204198, "relative_end": **********.204198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.204761, "relative_start": 1.7006809711456299, "end": **********.204761, "relative_end": **********.204761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.2067, "relative_start": 1.702620029449463, "end": **********.2067, "relative_end": **********.2067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.207549, "relative_start": 1.7034690380096436, "end": **********.207549, "relative_end": **********.207549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.210046, "relative_start": 1.7059659957885742, "end": **********.210046, "relative_end": **********.210046, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.211494, "relative_start": 1.707413911819458, "end": **********.211494, "relative_end": **********.211494, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.213396, "relative_start": 1.7093160152435303, "end": **********.213396, "relative_end": **********.213396, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.214594, "relative_start": 1.7105138301849365, "end": **********.214594, "relative_end": **********.214594, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.21542, "relative_start": 1.7113399505615234, "end": **********.21542, "relative_end": **********.21542, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.216947, "relative_start": 1.712867021560669, "end": **********.216947, "relative_end": **********.216947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.220318, "relative_start": 1.716238021850586, "end": **********.220318, "relative_end": **********.220318, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.220866, "relative_start": 1.7167859077453613, "end": **********.220866, "relative_end": **********.220866, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.223381, "relative_start": 1.7193009853363037, "end": **********.223381, "relative_end": **********.223381, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.223976, "relative_start": 1.719895839691162, "end": **********.223976, "relative_end": **********.223976, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.225727, "relative_start": 1.721647024154663, "end": **********.225727, "relative_end": **********.225727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.226113, "relative_start": 1.7220330238342285, "end": **********.226113, "relative_end": **********.226113, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.227192, "relative_start": 1.723111867904663, "end": **********.227192, "relative_end": **********.227192, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.228035, "relative_start": 1.7239549160003662, "end": **********.228035, "relative_end": **********.228035, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.228766, "relative_start": 1.7246859073638916, "end": **********.228766, "relative_end": **********.228766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.229725, "relative_start": 1.725644826889038, "end": **********.229725, "relative_end": **********.229725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.232088, "relative_start": 1.7280080318450928, "end": **********.232088, "relative_end": **********.232088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.232893, "relative_start": 1.7288129329681396, "end": **********.232893, "relative_end": **********.232893, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.23488, "relative_start": 1.730799913406372, "end": **********.23488, "relative_end": **********.23488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.235563, "relative_start": 1.731482982635498, "end": **********.235563, "relative_end": **********.235563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.237382, "relative_start": 1.7333018779754639, "end": **********.237382, "relative_end": **********.237382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.238017, "relative_start": 1.7339370250701904, "end": **********.238017, "relative_end": **********.238017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.23911, "relative_start": 1.735029935836792, "end": **********.23911, "relative_end": **********.23911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.239756, "relative_start": 1.7356760501861572, "end": **********.239756, "relative_end": **********.239756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.240247, "relative_start": 1.7361669540405273, "end": **********.240247, "relative_end": **********.240247, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.24062, "relative_start": 1.7365398406982422, "end": **********.24062, "relative_end": **********.24062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.242327, "relative_start": 1.7382469177246094, "end": **********.242327, "relative_end": **********.242327, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.242727, "relative_start": 1.7386469841003418, "end": **********.242727, "relative_end": **********.242727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.244337, "relative_start": 1.7402570247650146, "end": **********.244337, "relative_end": **********.244337, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.244873, "relative_start": 1.740792989730835, "end": **********.244873, "relative_end": **********.244873, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.246557, "relative_start": 1.7424769401550293, "end": **********.246557, "relative_end": **********.246557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.247062, "relative_start": 1.7429819107055664, "end": **********.247062, "relative_end": **********.247062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.24813, "relative_start": 1.7440500259399414, "end": **********.24813, "relative_end": **********.24813, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.248833, "relative_start": 1.7447528839111328, "end": **********.248833, "relative_end": **********.248833, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.249309, "relative_start": 1.7452290058135986, "end": **********.249309, "relative_end": **********.249309, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.249834, "relative_start": 1.7457540035247803, "end": **********.249834, "relative_end": **********.249834, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.251921, "relative_start": 1.7478408813476562, "end": **********.251921, "relative_end": **********.251921, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.252635, "relative_start": 1.7485549449920654, "end": **********.252635, "relative_end": **********.252635, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.254913, "relative_start": 1.7508330345153809, "end": **********.254913, "relative_end": **********.254913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.256166, "relative_start": 1.7520859241485596, "end": **********.256166, "relative_end": **********.256166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.261352, "relative_start": 1.7572720050811768, "end": **********.261352, "relative_end": **********.261352, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.262589, "relative_start": 1.7585089206695557, "end": **********.262589, "relative_end": **********.262589, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.265393, "relative_start": 1.7613129615783691, "end": **********.265393, "relative_end": **********.265393, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.266467, "relative_start": 1.7623870372772217, "end": **********.266467, "relative_end": **********.266467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.267291, "relative_start": 1.7632110118865967, "end": **********.267291, "relative_end": **********.267291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.267776, "relative_start": 1.7636959552764893, "end": **********.267776, "relative_end": **********.267776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.269476, "relative_start": 1.7653958797454834, "end": **********.269476, "relative_end": **********.269476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.26985, "relative_start": 1.7657699584960938, "end": **********.26985, "relative_end": **********.26985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.271916, "relative_start": 1.7678358554840088, "end": **********.271916, "relative_end": **********.271916, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.272492, "relative_start": 1.7684118747711182, "end": **********.272492, "relative_end": **********.272492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.274149, "relative_start": 1.770068883895874, "end": **********.274149, "relative_end": **********.274149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.274912, "relative_start": 1.7708320617675781, "end": **********.274912, "relative_end": **********.274912, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.276633, "relative_start": 1.7725529670715332, "end": **********.276633, "relative_end": **********.276633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.277683, "relative_start": 1.7736029624938965, "end": **********.277683, "relative_end": **********.277683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.279216, "relative_start": 1.7751359939575195, "end": **********.279216, "relative_end": **********.279216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.279818, "relative_start": 1.775738000869751, "end": **********.279818, "relative_end": **********.279818, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.28155, "relative_start": 1.7774698734283447, "end": **********.28155, "relative_end": **********.28155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.282084, "relative_start": 1.7780039310455322, "end": **********.282084, "relative_end": **********.282084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.284019, "relative_start": 1.7799389362335205, "end": **********.284019, "relative_end": **********.284019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.284628, "relative_start": 1.780547857284546, "end": **********.284628, "relative_end": **********.284628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.286601, "relative_start": 1.7825210094451904, "end": **********.286601, "relative_end": **********.286601, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.287333, "relative_start": 1.7832529544830322, "end": **********.287333, "relative_end": **********.287333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.288929, "relative_start": 1.784848928451538, "end": **********.288929, "relative_end": **********.288929, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.289953, "relative_start": 1.7858729362487793, "end": **********.289953, "relative_end": **********.289953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.290433, "relative_start": 1.7863528728485107, "end": **********.290433, "relative_end": **********.290433, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.290827, "relative_start": 1.7867469787597656, "end": **********.290827, "relative_end": **********.290827, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.292865, "relative_start": 1.7887849807739258, "end": **********.292865, "relative_end": **********.292865, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.293412, "relative_start": 1.7893319129943848, "end": **********.293412, "relative_end": **********.293412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.295191, "relative_start": 1.7911109924316406, "end": **********.295191, "relative_end": **********.295191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.295726, "relative_start": 1.7916460037231445, "end": **********.295726, "relative_end": **********.295726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.297466, "relative_start": 1.7933859825134277, "end": **********.297466, "relative_end": **********.297466, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.298235, "relative_start": 1.7941548824310303, "end": **********.298235, "relative_end": **********.298235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.2998, "relative_start": 1.795719861984253, "end": **********.2998, "relative_end": **********.2998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.300777, "relative_start": 1.7966969013214111, "end": **********.300777, "relative_end": **********.300777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.301242, "relative_start": 1.7971620559692383, "end": **********.301242, "relative_end": **********.301242, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.301698, "relative_start": 1.7976179122924805, "end": **********.301698, "relative_end": **********.301698, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.303688, "relative_start": 1.7996079921722412, "end": **********.303688, "relative_end": **********.303688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.304276, "relative_start": 1.8001959323883057, "end": **********.304276, "relative_end": **********.304276, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.306465, "relative_start": 1.802384853363037, "end": **********.306465, "relative_end": **********.306465, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.307096, "relative_start": 1.803015947341919, "end": **********.307096, "relative_end": **********.307096, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.30882, "relative_start": 1.8047399520874023, "end": **********.30882, "relative_end": **********.30882, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.309609, "relative_start": 1.8055288791656494, "end": **********.309609, "relative_end": **********.309609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.31128, "relative_start": 1.8071999549865723, "end": **********.31128, "relative_end": **********.31128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.312209, "relative_start": 1.808128833770752, "end": **********.312209, "relative_end": **********.312209, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.313603, "relative_start": 1.8095228672027588, "end": **********.313603, "relative_end": **********.313603, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.314828, "relative_start": 1.8107478618621826, "end": **********.314828, "relative_end": **********.314828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.31777, "relative_start": 1.813689947128296, "end": **********.31777, "relative_end": **********.31777, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.319029, "relative_start": 1.8149490356445312, "end": **********.319029, "relative_end": **********.319029, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.322547, "relative_start": 1.8184669017791748, "end": **********.322547, "relative_end": **********.322547, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.323533, "relative_start": 1.8194530010223389, "end": **********.323533, "relative_end": **********.323533, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.325897, "relative_start": 1.8218169212341309, "end": **********.325897, "relative_end": **********.325897, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.326553, "relative_start": 1.8224730491638184, "end": **********.326553, "relative_end": **********.326553, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.328576, "relative_start": 1.8244960308074951, "end": **********.328576, "relative_end": **********.328576, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.329499, "relative_start": 1.8254189491271973, "end": **********.329499, "relative_end": **********.329499, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.32995, "relative_start": 1.8258700370788574, "end": **********.32995, "relative_end": **********.32995, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.330296, "relative_start": 1.8262159824371338, "end": **********.330296, "relative_end": **********.330296, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.331613, "relative_start": 1.8275330066680908, "end": **********.331613, "relative_end": **********.331613, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.331857, "relative_start": 1.8277769088745117, "end": **********.331857, "relative_end": **********.331857, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.333102, "relative_start": 1.82902193069458, "end": **********.333102, "relative_end": **********.333102, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.334145, "relative_start": 1.8300650119781494, "end": **********.334145, "relative_end": **********.334145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.336557, "relative_start": 1.8324768543243408, "end": **********.336557, "relative_end": **********.336557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.337269, "relative_start": 1.8331890106201172, "end": **********.337269, "relative_end": **********.337269, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.338549, "relative_start": 1.8344688415527344, "end": **********.338549, "relative_end": **********.338549, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.339091, "relative_start": 1.8350110054016113, "end": **********.339091, "relative_end": **********.339091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.33936, "relative_start": 1.835279941558838, "end": **********.33936, "relative_end": **********.33936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.339693, "relative_start": 1.8356130123138428, "end": **********.339693, "relative_end": **********.339693, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.341837, "relative_start": 1.837756872177124, "end": **********.341837, "relative_end": **********.341837, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.3426, "relative_start": 1.8385200500488281, "end": **********.3426, "relative_end": **********.3426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.344325, "relative_start": 1.840245008468628, "end": **********.344325, "relative_end": **********.344325, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.344729, "relative_start": 1.840648889541626, "end": **********.344729, "relative_end": **********.344729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.345985, "relative_start": 1.841904878616333, "end": **********.345985, "relative_end": **********.345985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.346267, "relative_start": 1.8421869277954102, "end": **********.346267, "relative_end": **********.346267, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.347015, "relative_start": 1.8429348468780518, "end": **********.347015, "relative_end": **********.347015, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.349344, "relative_start": 1.845263957977295, "end": **********.349344, "relative_end": **********.349344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.359108, "relative_start": 1.8550279140472412, "end": **********.359108, "relative_end": **********.359108, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.359609, "relative_start": 1.8555288314819336, "end": **********.359609, "relative_end": **********.359609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.361222, "relative_start": 1.8571419715881348, "end": **********.361222, "relative_end": **********.361222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.361978, "relative_start": 1.8578979969024658, "end": **********.361978, "relative_end": **********.361978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.363729, "relative_start": 1.8596489429473877, "end": **********.363729, "relative_end": **********.363729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.364403, "relative_start": 1.8603229522705078, "end": **********.364403, "relative_end": **********.364403, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.366409, "relative_start": 1.8623290061950684, "end": **********.366409, "relative_end": **********.366409, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.366945, "relative_start": 1.8628649711608887, "end": **********.366945, "relative_end": **********.366945, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.367417, "relative_start": 1.8633370399475098, "end": **********.367417, "relative_end": **********.367417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.369266, "relative_start": 1.8651859760284424, "end": **********.369266, "relative_end": **********.369266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.371119, "relative_start": 1.8670389652252197, "end": **********.371119, "relative_end": **********.371119, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.371647, "relative_start": 1.8675668239593506, "end": **********.371647, "relative_end": **********.371647, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.374853, "relative_start": 1.8707728385925293, "end": **********.374853, "relative_end": **********.374853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.376869, "relative_start": 1.872788906097412, "end": **********.376869, "relative_end": **********.376869, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.377492, "relative_start": 1.8734118938446045, "end": **********.377492, "relative_end": **********.377492, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.heading", "start": **********.386691, "relative_start": 1.8826110363006592, "end": **********.386691, "relative_end": **********.386691, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": **********.387311, "relative_start": 1.8832309246063232, "end": **********.387311, "relative_end": **********.387311, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.header.index", "start": **********.390292, "relative_start": 1.88621187210083, "end": **********.390292, "relative_end": **********.390292, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.actions", "start": **********.391202, "relative_start": 1.8871219158172607, "end": **********.391202, "relative_end": **********.391202, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.39802, "relative_start": 1.8939399719238281, "end": **********.39802, "relative_end": **********.39802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.399222, "relative_start": 1.895141839981079, "end": **********.399222, "relative_end": **********.399222, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.401074, "relative_start": 1.89699387550354, "end": **********.401074, "relative_end": **********.401074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.413986, "relative_start": 1.9099059104919434, "end": **********.413986, "relative_end": **********.413986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.415285, "relative_start": 1.9112050533294678, "end": **********.415285, "relative_end": **********.415285, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.415719, "relative_start": 1.9116389751434326, "end": **********.415719, "relative_end": **********.415719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.417326, "relative_start": 1.9132459163665771, "end": **********.417326, "relative_end": **********.417326, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": **********.418527, "relative_start": 1.9144468307495117, "end": **********.418527, "relative_end": **********.418527, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.442408, "relative_start": 1.9383280277252197, "end": **********.442408, "relative_end": **********.442408, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.index", "start": **********.443341, "relative_start": 1.9392609596252441, "end": **********.443341, "relative_end": **********.443341, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.topbar.index", "start": **********.460231, "relative_start": 1.956151008605957, "end": **********.460231, "relative_end": **********.460231, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.461998, "relative_start": 1.9579179286956787, "end": **********.461998, "relative_end": **********.461998, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.463155, "relative_start": 1.9590749740600586, "end": **********.463155, "relative_end": **********.463155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.46366, "relative_start": 1.9595799446105957, "end": **********.46366, "relative_end": **********.46366, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.464732, "relative_start": 1.9606518745422363, "end": **********.464732, "relative_end": **********.464732, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.global-search.index", "start": **********.467021, "relative_start": 1.9629409313201904, "end": **********.467021, "relative_end": **********.467021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.global-search.field", "start": **********.467908, "relative_start": 1.9638278484344482, "end": **********.467908, "relative_end": **********.467908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.469038, "relative_start": 1.9649579524993896, "end": **********.469038, "relative_end": **********.469038, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.469467, "relative_start": 1.9653868675231934, "end": **********.469467, "relative_end": **********.469467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.470544, "relative_start": 1.9664640426635742, "end": **********.470544, "relative_end": **********.470544, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.471394, "relative_start": 1.9673140048980713, "end": **********.471394, "relative_end": **********.471394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fc6af43a80d1f8feb6964b2b41596895", "start": **********.473324, "relative_start": 1.9692440032958984, "end": **********.473324, "relative_end": **********.473324, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.language-switcher", "start": **********.475112, "relative_start": 1.971031904220581, "end": **********.475112, "relative_end": **********.475112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.user-menu", "start": **********.477836, "relative_start": 1.9737558364868164, "end": **********.477836, "relative_end": **********.477836, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.avatar.user", "start": **********.479394, "relative_start": 1.9753139019012451, "end": **********.479394, "relative_end": **********.479394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.avatar", "start": **********.482774, "relative_start": 1.978693962097168, "end": **********.482774, "relative_end": **********.482774, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.header", "start": **********.483303, "relative_start": 1.9792230129241943, "end": **********.483303, "relative_end": **********.483303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.483858, "relative_start": 1.9797780513763428, "end": **********.483858, "relative_end": **********.483858, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.index", "start": **********.484438, "relative_start": 1.9803578853607178, "end": **********.484438, "relative_end": **********.484438, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.484863, "relative_start": 1.9807829856872559, "end": **********.484863, "relative_end": **********.484863, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.485253, "relative_start": 1.981173038482666, "end": **********.485253, "relative_end": **********.485253, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.485724, "relative_start": 1.9816439151763916, "end": **********.485724, "relative_end": **********.485724, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.486198, "relative_start": 1.9821178913116455, "end": **********.486198, "relative_end": **********.486198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.486864, "relative_start": 1.9827840328216553, "end": **********.486864, "relative_end": **********.486864, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.487266, "relative_start": 1.9831860065460205, "end": **********.487266, "relative_end": **********.487266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.487759, "relative_start": 1.9836790561676025, "end": **********.487759, "relative_end": **********.487759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.488164, "relative_start": 1.984083890914917, "end": **********.488164, "relative_end": **********.488164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.489412, "relative_start": 1.9853320121765137, "end": **********.489412, "relative_end": **********.489412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.490097, "relative_start": 1.9860169887542725, "end": **********.490097, "relative_end": **********.490097, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.490342, "relative_start": 1.9862618446350098, "end": **********.490342, "relative_end": **********.490342, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.index", "start": **********.491434, "relative_start": 1.987354040145874, "end": **********.491434, "relative_end": **********.491434, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.logo", "start": **********.497411, "relative_start": 1.993330955505371, "end": **********.497411, "relative_end": **********.497411, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.498658, "relative_start": 1.9945778846740723, "end": **********.498658, "relative_end": **********.498658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.500808, "relative_start": 1.9967279434204102, "end": **********.500808, "relative_end": **********.500808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.501954, "relative_start": 1.9978740215301514, "end": **********.501954, "relative_end": **********.501954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.5027, "relative_start": 1.9986200332641602, "end": **********.5027, "relative_end": **********.5027, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.504127, "relative_start": 2.000046968460083, "end": **********.504127, "relative_end": **********.504127, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.505797, "relative_start": 2.0017168521881104, "end": **********.505797, "relative_end": **********.505797, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.507273, "relative_start": 2.003192901611328, "end": **********.507273, "relative_end": **********.507273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.508759, "relative_start": 2.004678964614868, "end": **********.508759, "relative_end": **********.508759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.510016, "relative_start": 2.0059359073638916, "end": **********.510016, "relative_end": **********.510016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.511683, "relative_start": 2.0076029300689697, "end": **********.511683, "relative_end": **********.511683, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.513333, "relative_start": 2.0092530250549316, "end": **********.513333, "relative_end": **********.513333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.514293, "relative_start": 2.0102128982543945, "end": **********.514293, "relative_end": **********.514293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.515486, "relative_start": 2.0114059448242188, "end": **********.515486, "relative_end": **********.515486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.516638, "relative_start": 2.0125579833984375, "end": **********.516638, "relative_end": **********.516638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.517792, "relative_start": 2.013711929321289, "end": **********.517792, "relative_end": **********.517792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.51886, "relative_start": 2.014780044555664, "end": **********.51886, "relative_end": **********.51886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.519974, "relative_start": 2.0158939361572266, "end": **********.519974, "relative_end": **********.519974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.520971, "relative_start": 2.0168910026550293, "end": **********.520971, "relative_end": **********.520971, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.522558, "relative_start": 2.0184779167175293, "end": **********.522558, "relative_end": **********.522558, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.524043, "relative_start": 2.019963026046753, "end": **********.524043, "relative_end": **********.524043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.525543, "relative_start": 2.021462917327881, "end": **********.525543, "relative_end": **********.525543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.526512, "relative_start": 2.0224318504333496, "end": **********.526512, "relative_end": **********.526512, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.528099, "relative_start": 2.0240190029144287, "end": **********.528099, "relative_end": **********.528099, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.529727, "relative_start": 2.025646924972534, "end": **********.529727, "relative_end": **********.529727, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.530577, "relative_start": 2.0264968872070312, "end": **********.530577, "relative_end": **********.530577, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.531696, "relative_start": 2.027616024017334, "end": **********.531696, "relative_end": **********.531696, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.533333, "relative_start": 2.0292530059814453, "end": **********.533333, "relative_end": **********.533333, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.535496, "relative_start": 2.0314159393310547, "end": **********.535496, "relative_end": **********.535496, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.536769, "relative_start": 2.032688856124878, "end": **********.536769, "relative_end": **********.536769, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.537278, "relative_start": 2.0331978797912598, "end": **********.537278, "relative_end": **********.537278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.538082, "relative_start": 2.0340018272399902, "end": **********.538082, "relative_end": **********.538082, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.53875, "relative_start": 2.034669876098633, "end": **********.53875, "relative_end": **********.53875, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.539557, "relative_start": 2.0354769229888916, "end": **********.539557, "relative_end": **********.539557, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.540151, "relative_start": 2.0360708236694336, "end": **********.540151, "relative_end": **********.540151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.540852, "relative_start": 2.0367720127105713, "end": **********.540852, "relative_end": **********.540852, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.541287, "relative_start": 2.0372068881988525, "end": **********.541287, "relative_end": **********.541287, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.542281, "relative_start": 2.038200855255127, "end": **********.542281, "relative_end": **********.542281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.543254, "relative_start": 2.0391738414764404, "end": **********.543254, "relative_end": **********.543254, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.544442, "relative_start": 2.0403618812561035, "end": **********.544442, "relative_end": **********.544442, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.545735, "relative_start": 2.0416548252105713, "end": **********.545735, "relative_end": **********.545735, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.546235, "relative_start": 2.0421550273895264, "end": **********.546235, "relative_end": **********.546235, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.546965, "relative_start": 2.0428848266601562, "end": **********.546965, "relative_end": **********.546965, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.547503, "relative_start": 2.0434229373931885, "end": **********.547503, "relative_end": **********.547503, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.548277, "relative_start": 2.044196844100952, "end": **********.548277, "relative_end": **********.548277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.54908, "relative_start": 2.044999837875366, "end": **********.54908, "relative_end": **********.54908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.549886, "relative_start": 2.0458059310913086, "end": **********.549886, "relative_end": **********.549886, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.550646, "relative_start": 2.0465660095214844, "end": **********.550646, "relative_end": **********.550646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.551782, "relative_start": 2.047701835632324, "end": **********.551782, "relative_end": **********.551782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.552947, "relative_start": 2.0488669872283936, "end": **********.552947, "relative_end": **********.552947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.553467, "relative_start": 2.049386978149414, "end": **********.553467, "relative_end": **********.553467, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.554149, "relative_start": 2.0500688552856445, "end": **********.554149, "relative_end": **********.554149, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.554669, "relative_start": 2.050588846206665, "end": **********.554669, "relative_end": **********.554669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.555427, "relative_start": 2.051347017288208, "end": **********.555427, "relative_end": **********.555427, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.556351, "relative_start": 2.0522708892822266, "end": **********.556351, "relative_end": **********.556351, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.55759, "relative_start": 2.0535099506378174, "end": **********.55759, "relative_end": **********.55759, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.558878, "relative_start": 2.054797887802124, "end": **********.558878, "relative_end": **********.558878, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.559369, "relative_start": 2.0552890300750732, "end": **********.559369, "relative_end": **********.559369, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.560147, "relative_start": 2.0560669898986816, "end": **********.560147, "relative_end": **********.560147, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.56081, "relative_start": 2.056730031967163, "end": **********.56081, "relative_end": **********.56081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.561685, "relative_start": 2.057605028152466, "end": **********.561685, "relative_end": **********.561685, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.562596, "relative_start": 2.058516025543213, "end": **********.562596, "relative_end": **********.562596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.564574, "relative_start": 2.0604939460754395, "end": **********.564574, "relative_end": **********.564574, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.56624, "relative_start": 2.062160015106201, "end": **********.56624, "relative_end": **********.56624, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.567, "relative_start": 2.062919855117798, "end": **********.567, "relative_end": **********.567, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.567828, "relative_start": 2.0637478828430176, "end": **********.567828, "relative_end": **********.567828, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.568377, "relative_start": 2.0642969608306885, "end": **********.568377, "relative_end": **********.568377, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.569074, "relative_start": 2.0649938583374023, "end": **********.569074, "relative_end": **********.569074, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.base", "start": **********.5703, "relative_start": 2.0662200450897217, "end": **********.5703, "relative_end": **********.5703, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.571781, "relative_start": 2.0677008628845215, "end": **********.571781, "relative_end": **********.571781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::27df4909b5b8fbf02f3a65ba2f758414", "start": **********.572853, "relative_start": 2.068773031234741, "end": **********.572853, "relative_end": **********.572853, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.577939, "relative_start": 2.0738589763641357, "end": **********.577939, "relative_end": **********.577939, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97c5322e947dbf60ae5ca480928dd519", "start": **********.578581, "relative_start": 2.0745010375976562, "end": **********.578581, "relative_end": **********.578581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.582702, "relative_start": 2.0786218643188477, "end": **********.582867, "relative_end": **********.582867, "duration": 0.00016498565673828125, "duration_str": "165μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.584666, "relative_start": 2.0805859565734863, "end": **********.584763, "relative_end": **********.584763, "duration": 9.703636169433594e-05, "duration_str": "97μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 50477888, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 336, "nb_templates": 336, "templates": [{"name": "1x filament.admin.resources.menu-item-resource.pages.manage-menu-items", "param_count": null, "params": [], "start": **********.563478, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.phpfilament.admin.resources.menu-item-resource.pages.manage-menu-items", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ffilament%2Fadmin%2Fresources%2Fmenu-item-resource%2Fpages%2Fmanage-menu-items.blade.php&line=1", "ajax": false, "filename": "manage-menu-items.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.admin.resources.menu-item-resource.pages.manage-menu-items"}, {"name": "133x filament::components.icon", "param_count": null, "params": [], "start": **********.574831, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 133, "name_original": "filament::components.icon"}, {"name": "63x filament::components.button.index", "param_count": null, "params": [], "start": **********.595147, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 63, "name_original": "filament::components.button.index"}, {"name": "45x filament::components.loading-indicator", "param_count": null, "params": [], "start": **********.600522, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 45, "name_original": "filament::components.loading-indicator"}, {"name": "17x filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "param_count": null, "params": [], "start": **********.141108, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/partials/wordpress-menu-item.blade.phpfilament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ffilament%2Fadmin%2Fresources%2Fmenu-item-resource%2Fpartials%2Fwordpress-menu-item.blade.php&line=1", "ajax": false, "filename": "wordpress-menu-item.blade.php", "line": "?"}, "render_count": 17, "name_original": "filament.admin.resources.menu-item-resource.partials.wordpress-menu-item"}, {"name": "1x filament::components.modal.index", "param_count": null, "params": [], "start": **********.371624, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.modal.index"}, {"name": "11x filament::components.icon-button", "param_count": null, "params": [], "start": **********.374829, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 11, "name_original": "filament::components.icon-button"}, {"name": "1x filament::components.modal.heading", "param_count": null, "params": [], "start": **********.386672, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/heading.blade.phpfilament::components.modal.heading", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.modal.heading"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": **********.387288, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament-panels::components.header.index", "param_count": null, "params": [], "start": **********.390263, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/header/index.blade.phpfilament-panels::components.header.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.header.index"}, {"name": "1x filament::components.actions", "param_count": null, "params": [], "start": **********.391184, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/actions.blade.phpfilament::components.actions", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.actions"}, {"name": "2x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.397974, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": **********.4185, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.442378, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x filament-panels::components.layout.index", "param_count": null, "params": [], "start": **********.443311, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/layout/index.blade.phpfilament-panels::components.layout.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.index"}, {"name": "1x filament-panels::components.topbar.index", "param_count": null, "params": [], "start": **********.46021, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/topbar/index.blade.phpfilament-panels::components.topbar.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftopbar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.topbar.index"}, {"name": "1x filament-panels::components.global-search.index", "param_count": null, "params": [], "start": **********.466997, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/global-search/index.blade.phpfilament-panels::components.global-search.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fglobal-search%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.global-search.index"}, {"name": "1x filament-panels::components.global-search.field", "param_count": null, "params": [], "start": **********.467885, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/global-search/field.blade.phpfilament-panels::components.global-search.field", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fglobal-search%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.global-search.field"}, {"name": "1x filament::components.input.index", "param_count": null, "params": [], "start": **********.469018, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/index.blade.phpfilament::components.input.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.input.index"}, {"name": "1x filament::components.input.wrapper", "param_count": null, "params": [], "start": **********.469451, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/wrapper.blade.phpfilament::components.input.wrapper", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fwrapper.blade.php&line=1", "ajax": false, "filename": "wrapper.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.input.wrapper"}, {"name": "1x __components::fc6af43a80d1f8feb6964b2b41596895", "param_count": null, "params": [], "start": **********.473298, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/fc6af43a80d1f8feb6964b2b41596895.blade.php__components::fc6af43a80d1f8feb6964b2b41596895", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Ffc6af43a80d1f8feb6964b2b41596895.blade.php&line=1", "ajax": false, "filename": "fc6af43a80d1f8feb6964b2b41596895.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fc6af43a80d1f8feb6964b2b41596895"}, {"name": "1x livewire.language-switcher", "param_count": null, "params": [], "start": **********.47509, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/language-switcher.blade.phplivewire.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.language-switcher"}, {"name": "1x filament-panels::components.user-menu", "param_count": null, "params": [], "start": **********.477806, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/user-menu.blade.phpfilament-panels::components.user-menu", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.user-menu"}, {"name": "1x filament-panels::components.avatar.user", "param_count": null, "params": [], "start": **********.47934, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/avatar/user.blade.phpfilament-panels::components.avatar.user", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Favatar%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.avatar.user"}, {"name": "1x filament::components.avatar", "param_count": null, "params": [], "start": **********.482755, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/avatar.blade.phpfilament::components.avatar", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.avatar"}, {"name": "1x filament::components.dropdown.header", "param_count": null, "params": [], "start": **********.483287, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/header.blade.phpfilament::components.dropdown.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.header"}, {"name": "1x filament-panels::components.theme-switcher.index", "param_count": null, "params": [], "start": **********.484423, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/theme-switcher/index.blade.phpfilament-panels::components.theme-switcher.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.theme-switcher.index"}, {"name": "3x filament-panels::components.theme-switcher.button", "param_count": null, "params": [], "start": **********.484844, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/theme-switcher/button.blade.phpfilament-panels::components.theme-switcher.button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.theme-switcher.button"}, {"name": "2x filament::components.dropdown.list.index", "param_count": null, "params": [], "start": **********.487743, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/list/index.blade.phpfilament::components.dropdown.list.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.list.index"}, {"name": "1x filament::components.dropdown.list.item", "param_count": null, "params": [], "start": **********.48815, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/list/item.blade.phpfilament::components.dropdown.list.item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.list.item"}, {"name": "1x filament::components.dropdown.index", "param_count": null, "params": [], "start": **********.490329, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/index.blade.phpfilament::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.index"}, {"name": "1x filament-panels::components.sidebar.index", "param_count": null, "params": [], "start": **********.49141, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/index.blade.phpfilament-panels::components.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar.index"}, {"name": "1x filament-panels::components.logo", "param_count": null, "params": [], "start": **********.497395, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/logo.blade.phpfilament-panels::components.logo", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.logo"}, {"name": "9x filament-panels::components.sidebar.group", "param_count": null, "params": [], "start": **********.49864, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/group.blade.phpfilament-panels::components.sidebar.group", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 9, "name_original": "filament-panels::components.sidebar.group"}, {"name": "21x filament-panels::components.sidebar.item", "param_count": null, "params": [], "start": **********.500751, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/item.blade.phpfilament-panels::components.sidebar.item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 21, "name_original": "filament-panels::components.sidebar.item"}, {"name": "1x filament-panels::components.layout.base", "param_count": null, "params": [], "start": **********.570279, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/layout/base.blade.phpfilament-panels::components.layout.base", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.base"}, {"name": "2x filament::assets", "param_count": null, "params": [], "start": **********.571763, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/assets.blade.phpfilament::assets", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fassets.blade.php&line=1", "ajax": false, "filename": "assets.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::assets"}, {"name": "1x __components::27df4909b5b8fbf02f3a65ba2f758414", "param_count": null, "params": [], "start": **********.572835, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/27df4909b5b8fbf02f3a65ba2f758414.blade.php__components::27df4909b5b8fbf02f3a65ba2f758414", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F27df4909b5b8fbf02f3a65ba2f758414.blade.php&line=1", "ajax": false, "filename": "27df4909b5b8fbf02f3a65ba2f758414.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::27df4909b5b8fbf02f3a65ba2f758414"}, {"name": "1x __components::97c5322e947dbf60ae5ca480928dd519", "param_count": null, "params": [], "start": **********.578565, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/97c5322e947dbf60ae5ca480928dd519.blade.php__components::97c5322e947dbf60ae5ca480928dd519", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F97c5322e947dbf60ae5ca480928dd519.blade.php&line=1", "ajax": false, "filename": "97c5322e947dbf60ae5ca480928dd519.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::97c5322e947dbf60ae5ca480928dd519"}]}, "queries": {"count": 176, "nb_statements": 176, "nb_visible_statements": 176, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.23787000000000005, "accumulated_duration_str": "238ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 76 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.1611252, "duration": 0.0025800000000000003, "duration_str": "2.58ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 1.085}, {"sql": "select * from `menus` where `menus`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 177}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 119}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.270942, "duration": 0.00174, "duration_str": "1.74ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:177", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=177", "ajax": false, "filename": "ManageMenuItems.php", "line": "177"}, "connection": "auvista", "explain": null, "start_percent": 1.085, "width_percent": 0.731}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.295446, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 1.816, "width_percent": 0.332}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.304532, "duration": 0.00172, "duration_str": "1.72ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 2.148, "width_percent": 0.723}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.4332051, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 2.871, "width_percent": 0.647}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4367938, "duration": 0.00237, "duration_str": "2.37ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 3.519, "width_percent": 0.996}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.44091, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 4.515, "width_percent": 0.66}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.443624, "duration": 0.0010500000000000002, "duration_str": "1.05ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 5.175, "width_percent": 0.441}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4463968, "duration": 0.00215, "duration_str": "2.15ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 5.617, "width_percent": 0.904}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.449857, "duration": 0.00214, "duration_str": "2.14ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 6.52, "width_percent": 0.9}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4537458, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 7.42, "width_percent": 0.605}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.456575, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 8.025, "width_percent": 0.626}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4601998, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 8.652, "width_percent": 1.068}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.464456, "duration": 0.0024500000000000004, "duration_str": "2.45ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 9.72, "width_percent": 1.03}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.468423, "duration": 0.0012900000000000001, "duration_str": "1.29ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 10.75, "width_percent": 0.542}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4710891, "duration": 0.00191, "duration_str": "1.91ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 11.292, "width_percent": 0.803}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.474436, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 12.095, "width_percent": 0.509}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.476672, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 12.604, "width_percent": 0.454}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.478933, "duration": 0.00175, "duration_str": "1.75ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 13.058, "width_percent": 0.736}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.482451, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 13.793, "width_percent": 0.727}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4857981, "duration": 0.00294, "duration_str": "2.94ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 14.521, "width_percent": 1.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.490121, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 15.757, "width_percent": 0.626}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.500238, "duration": 0.00157, "duration_str": "1.57ms", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 16.383, "width_percent": 0.66}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.503386, "duration": 0.0014399999999999999, "duration_str": "1.44ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 17.043, "width_percent": 0.605}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.510335, "duration": 0.00433, "duration_str": "4.33ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 17.648, "width_percent": 1.82}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.518768, "duration": 0.00348, "duration_str": "3.48ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 19.469, "width_percent": 1.463}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.5270681, "duration": 0.0024100000000000002, "duration_str": "2.41ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 20.932, "width_percent": 1.013}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.542474, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 21.945, "width_percent": 1.127}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.7323382, "duration": 0.00135, "duration_str": "1.35ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 23.071, "width_percent": 0.568}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.734962, "duration": 0.00217, "duration_str": "2.17ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 23.639, "width_percent": 0.912}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.7386389, "duration": 0.00196, "duration_str": "1.96ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 24.551, "width_percent": 0.824}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.741976, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 25.375, "width_percent": 0.593}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.746185, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 25.968, "width_percent": 0.858}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.749387, "duration": 0.00248, "duration_str": "2.48ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 26.826, "width_percent": 1.043}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.753509, "duration": 0.0014299999999999998, "duration_str": "1.43ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 27.868, "width_percent": 0.601}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.756373, "duration": 0.0013, "duration_str": "1.3ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 28.469, "width_percent": 0.547}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.7589118, "duration": 0.00109, "duration_str": "1.09ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 29.016, "width_percent": 0.458}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.761494, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 29.474, "width_percent": 0.53}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.763777, "duration": 0.00136, "duration_str": "1.36ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 30.004, "width_percent": 0.572}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.766228, "duration": 0.00095, "duration_str": "950μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 30.576, "width_percent": 0.399}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.768209, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 30.975, "width_percent": 0.324}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.7699509, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 31.299, "width_percent": 0.248}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.771715, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 31.547, "width_percent": 0.517}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.7739391, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 32.064, "width_percent": 0.29}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.775635, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 32.354, "width_percent": 0.324}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.7774198, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 32.678, "width_percent": 0.467}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.779999, "duration": 0.00121, "duration_str": "1.21ms", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 33.144, "width_percent": 0.509}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.782151, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 33.653, "width_percent": 0.332}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.784455, "duration": 0.0016200000000000001, "duration_str": "1.62ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 33.985, "width_percent": 0.681}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.787455, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 34.666, "width_percent": 0.336}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.789162, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 35.002, "width_percent": 0.24}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.790853, "duration": 0.0018, "duration_str": "1.8ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 35.242, "width_percent": 0.757}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.813468, "duration": 0.00186, "duration_str": "1.86ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 35.999, "width_percent": 0.782}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.816403, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 36.781, "width_percent": 0.324}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.818192, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 37.104, "width_percent": 0.282}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.819898, "duration": 0.0011200000000000001, "duration_str": "1.12ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 37.386, "width_percent": 0.471}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.82202, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 37.857, "width_percent": 0.273}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.823447, "duration": 0.00055, "duration_str": "550μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.13, "width_percent": 0.231}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8248188, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.361, "width_percent": 0.248}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.826309, "duration": 0.0011, "duration_str": "1.1ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.609, "width_percent": 0.462}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8284519, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.072, "width_percent": 0.328}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.830174, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.4, "width_percent": 0.298}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.83172, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.698, "width_percent": 0.269}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.833285, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.967, "width_percent": 0.521}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.835685, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.489, "width_percent": 0.416}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.837609, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.905, "width_percent": 0.362}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.839601, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 41.266, "width_percent": 0.87}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.843014, "duration": 0.00132, "duration_str": "1.32ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 42.136, "width_percent": 0.555}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8454292, "duration": 0.001, "duration_str": "1ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 42.691, "width_percent": 0.42}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.847658, "duration": 0.00197, "duration_str": "1.97ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 43.112, "width_percent": 0.828}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.850574, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 43.94, "width_percent": 0.294}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8520768, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 44.234, "width_percent": 0.332}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8548472, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 44.566, "width_percent": 0.858}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8586779, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 45.424, "width_percent": 0.626}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.862074, "duration": 0.00198, "duration_str": "1.98ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 46.05, "width_percent": 0.832}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.86556, "duration": 0.00127, "duration_str": "1.27ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 46.883, "width_percent": 0.534}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.909001, "duration": 0.0024, "duration_str": "2.4ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 47.417, "width_percent": 1.009}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.913758, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 48.426, "width_percent": 0.811}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.917366, "duration": 0.00451, "duration_str": "4.51ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 49.237, "width_percent": 1.896}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.92341, "duration": 0.0030299999999999997, "duration_str": "3.03ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 51.133, "width_percent": 1.274}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.928948, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 52.407, "width_percent": 0.727}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.933227, "duration": 0.00366, "duration_str": "3.66ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 53.134, "width_percent": 1.539}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.9394422, "duration": 0.00413, "duration_str": "4.13ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 54.673, "width_percent": 1.736}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.946154, "duration": 0.00438, "duration_str": "4.38ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 56.409, "width_percent": 1.841}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.953575, "duration": 0.00317, "duration_str": "3.17ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 58.25, "width_percent": 1.333}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.959102, "duration": 0.00285, "duration_str": "2.85ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 59.583, "width_percent": 1.198}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.9651132, "duration": 0.0033, "duration_str": "3.3ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 60.781, "width_percent": 1.387}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.9729478, "duration": 0.005849999999999999, "duration_str": "5.85ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 62.168, "width_percent": 2.459}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.981341, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 64.628, "width_percent": 1.345}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.9863138, "duration": 0.00254, "duration_str": "2.54ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 65.973, "width_percent": 1.068}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.990491, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 67.041, "width_percent": 1.059}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.995187, "duration": 0.00265, "duration_str": "2.65ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 68.1, "width_percent": 1.114}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.999819, "duration": 0.00257, "duration_str": "2.57ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 69.214, "width_percent": 1.08}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.003813, "duration": 0.00151, "duration_str": "1.51ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 70.295, "width_percent": 0.635}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.0067132, "duration": 0.00207, "duration_str": "2.07ms", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 70.929, "width_percent": 0.87}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.010023, "duration": 0.00115, "duration_str": "1.15ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 71.8, "width_percent": 0.483}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.0125499, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 72.283, "width_percent": 0.32}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.014649, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 72.603, "width_percent": 0.298}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.016356, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 72.901, "width_percent": 0.252}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.018112, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 73.153, "width_percent": 0.391}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0402179, "duration": 0.00131, "duration_str": "1.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.544, "width_percent": 0.551}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.042522, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.095, "width_percent": 0.652}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.044667, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.747, "width_percent": 0.353}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0459168, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.1, "width_percent": 0.328}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.047179, "duration": 0.00088, "duration_str": "880μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.428, "width_percent": 0.37}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.048608, "duration": 0.0015400000000000001, "duration_str": "1.54ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.798, "width_percent": 0.647}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0507371, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.445, "width_percent": 0.425}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.052263, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.87, "width_percent": 0.408}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.053872, "duration": 0.0011899999999999999, "duration_str": "1.19ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.278, "width_percent": 0.5}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.055703, "duration": 0.0014, "duration_str": "1.4ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.778, "width_percent": 0.589}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.057649, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.366, "width_percent": 0.475}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.05935, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.841, "width_percent": 0.446}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.060919, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 79.287, "width_percent": 0.353}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0622492, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 79.64, "width_percent": 0.492}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.064106, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.132, "width_percent": 0.517}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.065801, "duration": 0.00106, "duration_str": "1.06ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.649, "width_percent": 0.446}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0673058, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.095, "width_percent": 0.324}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.06862, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.418, "width_percent": 0.332}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.070438, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.751, "width_percent": 0.698}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0727758, "duration": 0.00126, "duration_str": "1.26ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.448, "width_percent": 0.53}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.075265, "duration": 0.00213, "duration_str": "2.13ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.978, "width_percent": 0.895}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.07844, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 83.874, "width_percent": 0.538}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.080452, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.412, "width_percent": 0.273}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0818691, "duration": 0.00139, "duration_str": "1.39ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.685, "width_percent": 0.584}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.088002, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.269, "width_percent": 0.727}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.090289, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.997, "width_percent": 0.412}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.09171, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.409, "width_percent": 0.282}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.09279, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.69, "width_percent": 0.298}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0939682, "duration": 0.00098, "duration_str": "980μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.989, "width_percent": 0.412}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.095613, "duration": 0.0011799999999999998, "duration_str": "1.18ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.401, "width_percent": 0.496}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.098374, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.897, "width_percent": 1.038}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.101549, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.935, "width_percent": 0.622}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.103612, "duration": 0.00141, "duration_str": "1.41ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.557, "width_percent": 0.593}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.105695, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.15, "width_percent": 0.631}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.107693, "duration": 0.00047999999999999996, "duration_str": "480μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.781, "width_percent": 0.202}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.108532, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.982, "width_percent": 0.164}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1092849, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.146, "width_percent": 0.151}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.109936, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.298, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.11076, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.542, "width_percent": 0.282}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1118171, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.823, "width_percent": 0.269}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.11284, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.092, "width_percent": 0.219}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.113815, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.311, "width_percent": 0.206}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1146562, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.517, "width_percent": 0.185}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1154368, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.702, "width_percent": 0.177}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.11661, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.878, "width_percent": 0.189}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.117507, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.068, "width_percent": 0.177}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.118409, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.244, "width_percent": 0.294}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.119674, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.538, "width_percent": 0.164}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.12178, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.702, "width_percent": 0.374}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.123096, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.077, "width_percent": 0.151}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.123786, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.228, "width_percent": 0.135}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.124408, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.362, "width_percent": 0.122}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1250951, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.484, "width_percent": 0.366}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.126396, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.85, "width_percent": 0.219}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.127235, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.069, "width_percent": 0.139}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.127877, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.207, "width_percent": 0.135}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.128494, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.342, "width_percent": 0.122}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.129066, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.464, "width_percent": 0.126}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.129679, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.59, "width_percent": 0.126}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.130308, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.716, "width_percent": 0.181}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.131109, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 95.897, "width_percent": 0.252}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1321619, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.149, "width_percent": 0.391}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.133516, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.54, "width_percent": 0.357}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1346211, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.897, "width_percent": 0.118}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.135122, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.015, "width_percent": 0.13}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.135667, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.145, "width_percent": 0.114}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.136173, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.259, "width_percent": 0.147}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.136765, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.406, "width_percent": 0.122}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.137691, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.528, "width_percent": 0.151}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.138459, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.679, "width_percent": 0.168}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.1392598, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 97.848, "width_percent": 0.248}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.140265, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.096, "width_percent": 0.13}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.3479211, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.226, "width_percent": 0.433}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = ? and `model_has_roles`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.448889, "duration": 0.0015, "duration_str": "1.5ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 98.659, "width_percent": 0.631}, {"sql": "select count(*) as aggregate from `contact_forms` where `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.453992, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 99.29, "width_percent": 0.328}, {"sql": "select count(*) as aggregate from `contact_forms` where `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.494085, "duration": 0.00091, "duration_str": "910μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 99.617, "width_percent": 0.383}]}, "models": {"data": {"App\\Models\\MenuItem": {"value": 238, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Category": {"value": 210, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\StaticPage": {"value": 196, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FStaticPage.php&line=1", "ajax": false, "filename": "StaticPage.php", "line": "?"}}, "App\\Models\\Brand": {"value": 126, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Post": {"value": 113, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Menu": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}}, "count": 975, "is_counter": true}, "livewire": {"data": {"app.filament.resources.menu-item-resource.pages.manage-menu-items #UA8Iel46Bb0E4fV8DH4T": "array:4 [\n  \"data\" => array:51 [\n    \"isMenuReordering\" => false\n    \"newMenuItemData\" => null\n    \"selectedTab\" => \"custom\"\n    \"customUrl\" => null\n    \"customTitle\" => null\n    \"selectedPages\" => []\n    \"selectedPosts\" => []\n    \"selectedCategories\" => []\n    \"selectedBrands\" => []\n    \"selectedMenuItems\" => []\n    \"editingMenuItem\" => []\n    \"refreshCounter\" => 0\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:2 [\n      \"type\" => array:1 [\n        \"value\" => null\n      ]\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:3 [\n      \"url\" => true\n      \"type\" => true\n      \"status\" => true\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.menu-item-resource.pages.manage-menu-items\"\n  \"component\" => \"App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems\"\n  \"id\" => \"UA8Iel46Bb0E4fV8DH4T\"\n]", "filament.livewire.global-search #3Ggw9ktju6fENGJLdFCx": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"3Ggw9ktju6fENGJLdFCx\"\n]", "language-switcher #rmv7DQiKcSRCvETnozh9": "array:4 [\n  \"data\" => []\n  \"name\" => \"language-switcher\"\n  \"component\" => \"App\\Livewire\\LanguageSwitcher\"\n  \"id\" => \"rmv7DQiKcSRCvETnozh9\"\n]", "filament.livewire.notifications #80A0oZzmBmB0znfNvPcv": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2254\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"80A0oZzmBmB0znfNvPcv\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/admin/menu-items/5", "action_name": "filament.admin.resources.menu-items.index", "controller_action": "App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems", "uri": "GET admin/menu-items/{menu}", "controller": "App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems@render<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/menu-items", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, App\\Http\\Middleware\\LogLogoutMiddleware, App\\Http\\Middleware\\LocaleMiddleware, Filament\\Http\\Middleware\\Authenticate", "duration": "2.09s", "peak_memory": "54MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://auvista.test/admin/menus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IkVHdVZFMENrajFsM3E0K0xQL3NnOHc9PSIsInZhbHVlIjoiQXNyREY0Uk01dzhraTY2cTdhTWE2VlorbVUxY2hMb2owYzNYeTg4Y0xBdE9Pcm9weDJGc0NyZ284c2NXcHY5TUJlMmJTZGVVOXZIT1hLVjlVcUFZZkZlSUwxbW5pK3MxT2JjMG4zUFA4TWhWMWhHY1F2ZFFNWFExV3JoN2ljTXUiLCJtYWMiOiJmY2I0OWNiOTU0NDc3ZDM0NzUxZTE3MDQxN2YzYmY4ZTQ0NjY4ZDAyNGZlOGQ0ZDg0MmJmNjI4MjFlOWY5NDI5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6Imc0ZVBBMTVoZXdKTEt0ZVlOVFBWZ3c9PSIsInZhbHVlIjoieFEydUgrb25ZUndyd3piT2ZIQWEvdStwcm1PSjBjY3hZb1ZjVm41ZDhVdzM5L0xrUHQ1OURMQ0lJRU92YXMyT3RxdkIrRkUvWjh5TEw4REVHUU9kVW1aZm93RlduYUJudWswcTRxZnFDSkJTSUVkaS9jQzRGYkZ1enRibUZCc3MiLCJtYWMiOiIxMDc0YzZhZTA5OTBhODk0MTBlM2E0ZDMxNmM4MmQ3Zjk0YzQ3YWQ2MmNjZjA4MWVjZjVkYmJiNDU1YjllMzFmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-708245220 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ALiu3MXg2qeWtslysnYzKUjGVbJ1TKvwt3KhqDy1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-708245220\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-702340838 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 04:44:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IkJvc2Q2akxoNEpva3Zyc0g3bSs0dlE9PSIsInZhbHVlIjoiRCtWaEQxS0F3UG9MdDcyckw4cmhZdWdvOWoyMVIydXdQWlppeEtzRmNWNHU3K0tjMmQvb0V3MnJMYnBiYXVvRXVkSlorYXFWd1ZYQ25EK2lpQzc3eER1R2RRaEdPZXEyRjRKdlFPUkx5eVVXS0U3MGpwY2tXZEhtaTBub3B5QmQiLCJtYWMiOiJlNzZlNjBmNjBjM2JkNTYyZWQzNzIxMWVhYmFiNmY0ODM1MWFmOTNmN2ZjODg5NDlhNTU1OGIyYjNjZmE2ODgwIiwidGFnIjoiIn0%3D; expires=Thu, 17 Jul 2025 06:44:52 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InBIL1BwSE1uYmZOY20rZSt1cHhsc1E9PSIsInZhbHVlIjoiY2EyRkJOVnZjNVJpNFFjVlByelhNS3hqYVh4N2F1NVVGR29lbSs0TXBpTEIxTzlUb1FLbndaRmc2eUJ0Ym95T2JNQWxpa1dNbWdIUnY3YTY2WG1rY1dpYVdoUm1KRmovZysyRjZRRHNzbS8vUkFMNm12bHpTb3lxSTBrZ1JwbW4iLCJtYWMiOiI4NTg4NDk0ZTQ0OWE5NTVhZTJiZGU4ZmNhZDBkZWIxOTg0YzIwMWY2MWEwYjllYjdiYzcyOTdlZDYwNmRmMTkyIiwidGFnIjoiIn0%3D; expires=Thu, 17 Jul 2025 06:44:52 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IkJvc2Q2akxoNEpva3Zyc0g3bSs0dlE9PSIsInZhbHVlIjoiRCtWaEQxS0F3UG9MdDcyckw4cmhZdWdvOWoyMVIydXdQWlppeEtzRmNWNHU3K0tjMmQvb0V3MnJMYnBiYXVvRXVkSlorYXFWd1ZYQ25EK2lpQzc3eER1R2RRaEdPZXEyRjRKdlFPUkx5eVVXS0U3MGpwY2tXZEhtaTBub3B5QmQiLCJtYWMiOiJlNzZlNjBmNjBjM2JkNTYyZWQzNzIxMWVhYmFiNmY0ODM1MWFmOTNmN2ZjODg5NDlhNTU1OGIyYjNjZmE2ODgwIiwidGFnIjoiIn0%3D; expires=Thu, 17-Jul-2025 06:44:52 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InBIL1BwSE1uYmZOY20rZSt1cHhsc1E9PSIsInZhbHVlIjoiY2EyRkJOVnZjNVJpNFFjVlByelhNS3hqYVh4N2F1NVVGR29lbSs0TXBpTEIxTzlUb1FLbndaRmc2eUJ0Ym95T2JNQWxpa1dNbWdIUnY3YTY2WG1rY1dpYVdoUm1KRmovZysyRjZRRHNzbS8vUkFMNm12bHpTb3lxSTBrZ1JwbW4iLCJtYWMiOiI4NTg4NDk0ZTQ0OWE5NTVhZTJiZGU4ZmNhZDBkZWIxOTg0YzIwMWY2MWEwYjllYjdiYzcyOTdlZDYwNmRmMTkyIiwidGFnIjoiIn0%3D; expires=Thu, 17-Jul-2025 06:44:52 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-702340838\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-874817750 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">https://auvista.test/admin/menu-items/5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$2uJGkMnwk7Lj203GgLThU.EI1Z7VNTd1oXu5zgFDqiWVi2sEnDYEy</span>\"\n  \"<span class=sf-dump-key>current_menu_id</span>\" => <span class=sf-dump-num>5</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-874817750\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/admin/menu-items/5", "action_name": "filament.admin.resources.menu-items.index", "controller_action": "App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems"}, "badge": null}}