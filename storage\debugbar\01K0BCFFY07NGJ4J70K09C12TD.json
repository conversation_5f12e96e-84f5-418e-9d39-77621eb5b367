{"__meta": {"id": "01K0BCFFY07NGJ4J70K09C12TD", "datetime": "2025-07-17 12:07:26", "utime": **********.273304, "method": "GET", "uri": "/admin/menu-items/5", "ip": "127.0.0.1"}, "messages": {"count": 8, "messages": [{"message": "[12:07:20] LOG.info: Menu ID set from mount method {\n    \"menu_id\": 5\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.180618, "xdebug_link": null, "collector": "log"}, {"message": "[12:07:20] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.36362, "xdebug_link": null, "collector": "log"}, {"message": "[12:07:25] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.796435, "xdebug_link": null, "collector": "log"}, {"message": "[12:07:25] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.83804, "xdebug_link": null, "collector": "log"}, {"message": "[12:07:25] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.884041, "xdebug_link": null, "collector": "log"}, {"message": "[12:07:25] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.917471, "xdebug_link": null, "collector": "log"}, {"message": "[12:07:25] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.934022, "xdebug_link": null, "collector": "log"}, {"message": "[12:07:25] LOG.info: Menu getViewData called {\n    \"locale\": \"vi\",\n    \"app_locale\": \"vi\",\n    \"session_locale\": null,\n    \"menu_id\": 5,\n    \"refresh_counter\": 0\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.951347, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.271465, "end": **********.273365, "duration": 7.00189995765686, "duration_str": "7s", "measures": [{"label": "Booting", "start": **********.271465, "relative_start": 0, "end": **********.72011, "relative_end": **********.72011, "duration": 0.*****************, "duration_str": "449ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.720125, "relative_start": 0.*****************, "end": **********.273369, "relative_end": 4.0531158447265625e-06, "duration": 6.***************, "duration_str": "6.55s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.067393, "relative_start": 0.****************, "end": **********.080097, "relative_end": **********.080097, "duration": 0.012703895568847656, "duration_str": "12.7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: filament.admin.resources.menu-item-resource.pages.manage-menu-items", "start": **********.443936, "relative_start": 1.***************, "end": **********.443936, "relative_end": **********.443936, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.782419, "relative_start": 6.***************, "end": **********.782419, "relative_end": **********.782419, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.784286, "relative_start": 6.5128209590911865, "end": **********.784286, "relative_end": **********.784286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.78669, "relative_start": 6.515224933624268, "end": **********.78669, "relative_end": **********.78669, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.787043, "relative_start": 6.515578031539917, "end": **********.787043, "relative_end": **********.787043, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.787516, "relative_start": 6.5160510540008545, "end": **********.787516, "relative_end": **********.787516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.789034, "relative_start": 6.517568826675415, "end": **********.789034, "relative_end": **********.789034, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.79016, "relative_start": 6.518694877624512, "end": **********.79016, "relative_end": **********.79016, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.791076, "relative_start": 6.51961088180542, "end": **********.791076, "relative_end": **********.791076, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.792437, "relative_start": 6.520972013473511, "end": **********.792437, "relative_end": **********.792437, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.793065, "relative_start": 6.521600008010864, "end": **********.793065, "relative_end": **********.793065, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.793808, "relative_start": 6.522342920303345, "end": **********.793808, "relative_end": **********.793808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.794164, "relative_start": 6.522698879241943, "end": **********.794164, "relative_end": **********.794164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.794609, "relative_start": 6.523144006729126, "end": **********.794609, "relative_end": **********.794609, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.795516, "relative_start": 6.524050951004028, "end": **********.795516, "relative_end": **********.795516, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.832792, "relative_start": 6.56132698059082, "end": **********.832792, "relative_end": **********.832792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.833922, "relative_start": 6.562456846237183, "end": **********.833922, "relative_end": **********.833922, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.835772, "relative_start": 6.564306974411011, "end": **********.835772, "relative_end": **********.835772, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.836252, "relative_start": 6.564786911010742, "end": **********.836252, "relative_end": **********.836252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.836914, "relative_start": 6.565448999404907, "end": **********.836914, "relative_end": **********.836914, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.837418, "relative_start": 6.565953016281128, "end": **********.837418, "relative_end": **********.837418, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.875932, "relative_start": 6.604466915130615, "end": **********.875932, "relative_end": **********.875932, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.876461, "relative_start": 6.604995965957642, "end": **********.876461, "relative_end": **********.876461, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.878204, "relative_start": 6.606739044189453, "end": **********.878204, "relative_end": **********.878204, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.878743, "relative_start": 6.607277870178223, "end": **********.878743, "relative_end": **********.878743, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.87979, "relative_start": 6.608325004577637, "end": **********.87979, "relative_end": **********.87979, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.880277, "relative_start": 6.608811855316162, "end": **********.880277, "relative_end": **********.880277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.880524, "relative_start": 6.609058856964111, "end": **********.880524, "relative_end": **********.880524, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.882165, "relative_start": 6.610699892044067, "end": **********.882165, "relative_end": **********.882165, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.882637, "relative_start": 6.6111719608306885, "end": **********.882637, "relative_end": **********.882637, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.883331, "relative_start": 6.611865997314453, "end": **********.883331, "relative_end": **********.883331, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.914479, "relative_start": 6.643013954162598, "end": **********.914479, "relative_end": **********.914479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.915911, "relative_start": 6.644445896148682, "end": **********.915911, "relative_end": **********.915911, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.916164, "relative_start": 6.644698858261108, "end": **********.916164, "relative_end": **********.916164, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.916967, "relative_start": 6.6455018520355225, "end": **********.916967, "relative_end": **********.916967, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.931563, "relative_start": 6.66009783744812, "end": **********.931563, "relative_end": **********.931563, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.932959, "relative_start": 6.661494016647339, "end": **********.932959, "relative_end": **********.932959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.933166, "relative_start": 6.661700963973999, "end": **********.933166, "relative_end": **********.933166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.93358, "relative_start": 6.662114858627319, "end": **********.93358, "relative_end": **********.93358, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.950784, "relative_start": 6.679318904876709, "end": **********.950784, "relative_end": **********.950784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.964002, "relative_start": 6.6925368309021, "end": **********.964002, "relative_end": **********.964002, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.965261, "relative_start": 6.693795919418335, "end": **********.965261, "relative_end": **********.965261, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.965763, "relative_start": 6.694298028945923, "end": **********.965763, "relative_end": **********.965763, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.966487, "relative_start": 6.695021867752075, "end": **********.966487, "relative_end": **********.966487, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.966808, "relative_start": 6.695343017578125, "end": **********.966808, "relative_end": **********.966808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.968048, "relative_start": 6.696583032608032, "end": **********.968048, "relative_end": **********.968048, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.968252, "relative_start": 6.696786880493164, "end": **********.968252, "relative_end": **********.968252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.96942, "relative_start": 6.697954893112183, "end": **********.96942, "relative_end": **********.96942, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.970365, "relative_start": 6.698899984359741, "end": **********.970365, "relative_end": **********.970365, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.971713, "relative_start": 6.7002480030059814, "end": **********.971713, "relative_end": **********.971713, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.972081, "relative_start": 6.700615882873535, "end": **********.972081, "relative_end": **********.972081, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.972951, "relative_start": 6.701485872268677, "end": **********.972951, "relative_end": **********.972951, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.973312, "relative_start": 6.7018468379974365, "end": **********.973312, "relative_end": **********.973312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.973561, "relative_start": 6.702095985412598, "end": **********.973561, "relative_end": **********.973561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.97382, "relative_start": 6.702354907989502, "end": **********.97382, "relative_end": **********.97382, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.975026, "relative_start": 6.703560829162598, "end": **********.975026, "relative_end": **********.975026, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.975216, "relative_start": 6.703750848770142, "end": **********.975216, "relative_end": **********.975216, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.976317, "relative_start": 6.704851865768433, "end": **********.976317, "relative_end": **********.976317, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.976596, "relative_start": 6.7051310539245605, "end": **********.976596, "relative_end": **********.976596, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.977722, "relative_start": 6.706256866455078, "end": **********.977722, "relative_end": **********.977722, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.977953, "relative_start": 6.7064878940582275, "end": **********.977953, "relative_end": **********.977953, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.978652, "relative_start": 6.707186937332153, "end": **********.978652, "relative_end": **********.978652, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.978957, "relative_start": 6.707491874694824, "end": **********.978957, "relative_end": **********.978957, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.979198, "relative_start": 6.707732915878296, "end": **********.979198, "relative_end": **********.979198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.979454, "relative_start": 6.707988977432251, "end": **********.979454, "relative_end": **********.979454, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.980616, "relative_start": 6.709151029586792, "end": **********.980616, "relative_end": **********.980616, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.980808, "relative_start": 6.709342956542969, "end": **********.980808, "relative_end": **********.980808, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.981915, "relative_start": 6.710449934005737, "end": **********.981915, "relative_end": **********.981915, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.982185, "relative_start": 6.71071982383728, "end": **********.982185, "relative_end": **********.982185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.983312, "relative_start": 6.711846828460693, "end": **********.983312, "relative_end": **********.983312, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.983537, "relative_start": 6.712071895599365, "end": **********.983537, "relative_end": **********.983537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.984227, "relative_start": 6.712761878967285, "end": **********.984227, "relative_end": **********.984227, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.98453, "relative_start": 6.713064908981323, "end": **********.98453, "relative_end": **********.98453, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.984775, "relative_start": 6.71331000328064, "end": **********.984775, "relative_end": **********.984775, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.985123, "relative_start": 6.713657855987549, "end": **********.985123, "relative_end": **********.985123, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.986359, "relative_start": 6.714893817901611, "end": **********.986359, "relative_end": **********.986359, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.986548, "relative_start": 6.715082883834839, "end": **********.986548, "relative_end": **********.986548, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.987627, "relative_start": 6.7161619663238525, "end": **********.987627, "relative_end": **********.987627, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.987885, "relative_start": 6.71641993522644, "end": **********.987885, "relative_end": **********.987885, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.988978, "relative_start": 6.717512845993042, "end": **********.988978, "relative_end": **********.988978, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.989191, "relative_start": 6.717725992202759, "end": **********.989191, "relative_end": **********.989191, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.989874, "relative_start": 6.718408823013306, "end": **********.989874, "relative_end": **********.989874, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.990163, "relative_start": 6.718698024749756, "end": **********.990163, "relative_end": **********.990163, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.9904, "relative_start": 6.718935012817383, "end": **********.9904, "relative_end": **********.9904, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.99064, "relative_start": 6.719174861907959, "end": **********.99064, "relative_end": **********.99064, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.991796, "relative_start": 6.7203309535980225, "end": **********.991796, "relative_end": **********.991796, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.991977, "relative_start": 6.7205119132995605, "end": **********.991977, "relative_end": **********.991977, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.993047, "relative_start": 6.721581935882568, "end": **********.993047, "relative_end": **********.993047, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.993304, "relative_start": 6.72183895111084, "end": **********.993304, "relative_end": **********.993304, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.994405, "relative_start": 6.722939968109131, "end": **********.994405, "relative_end": **********.994405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.994618, "relative_start": 6.7231528759002686, "end": **********.994618, "relative_end": **********.994618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.995297, "relative_start": 6.72383189201355, "end": **********.995297, "relative_end": **********.995297, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.995584, "relative_start": 6.724118947982788, "end": **********.995584, "relative_end": **********.995584, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.995817, "relative_start": 6.72435188293457, "end": **********.995817, "relative_end": **********.995817, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.996058, "relative_start": 6.724592924118042, "end": **********.996058, "relative_end": **********.996058, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.997184, "relative_start": 6.725718975067139, "end": **********.997184, "relative_end": **********.997184, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.99736, "relative_start": 6.725894927978516, "end": **********.99736, "relative_end": **********.99736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.998429, "relative_start": 6.726963996887207, "end": **********.998429, "relative_end": **********.998429, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.998681, "relative_start": 6.727216005325317, "end": **********.998681, "relative_end": **********.998681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.999823, "relative_start": 6.728358030319214, "end": **********.999823, "relative_end": **********.999823, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.000051, "relative_start": 6.728585958480835, "end": **********.000051, "relative_end": **********.000051, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.000734, "relative_start": 6.729269027709961, "end": **********.000734, "relative_end": **********.000734, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.001157, "relative_start": 6.729691982269287, "end": **********.001157, "relative_end": **********.001157, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.00142, "relative_start": 6.729954957962036, "end": **********.00142, "relative_end": **********.00142, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.001675, "relative_start": 6.730209827423096, "end": **********.001675, "relative_end": **********.001675, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.00295, "relative_start": 6.731484889984131, "end": **********.00295, "relative_end": **********.00295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.003155, "relative_start": 6.731689929962158, "end": **********.003155, "relative_end": **********.003155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.004479, "relative_start": 6.733013868331909, "end": **********.004479, "relative_end": **********.004479, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.004856, "relative_start": 6.733391046524048, "end": **********.004856, "relative_end": **********.004856, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.006019, "relative_start": 6.734554052352905, "end": **********.006019, "relative_end": **********.006019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.006278, "relative_start": 6.73481297492981, "end": **********.006278, "relative_end": **********.006278, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.006983, "relative_start": 6.735517978668213, "end": **********.006983, "relative_end": **********.006983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.007293, "relative_start": 6.735827922821045, "end": **********.007293, "relative_end": **********.007293, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.007539, "relative_start": 6.736073970794678, "end": **********.007539, "relative_end": **********.007539, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.007792, "relative_start": 6.7363269329071045, "end": **********.007792, "relative_end": **********.007792, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.008952, "relative_start": 6.737486839294434, "end": **********.008952, "relative_end": **********.008952, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.009143, "relative_start": 6.737678050994873, "end": **********.009143, "relative_end": **********.009143, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.010244, "relative_start": 6.738778829574585, "end": **********.010244, "relative_end": **********.010244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.010518, "relative_start": 6.739053010940552, "end": **********.010518, "relative_end": **********.010518, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.011687, "relative_start": 6.740221977233887, "end": **********.011687, "relative_end": **********.011687, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.011933, "relative_start": 6.7404680252075195, "end": **********.011933, "relative_end": **********.011933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.012625, "relative_start": 6.741159915924072, "end": **********.012625, "relative_end": **********.012625, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.012927, "relative_start": 6.741461992263794, "end": **********.012927, "relative_end": **********.012927, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.013166, "relative_start": 6.741700887680054, "end": **********.013166, "relative_end": **********.013166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.013417, "relative_start": 6.741951942443848, "end": **********.013417, "relative_end": **********.013417, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.014571, "relative_start": 6.743105888366699, "end": **********.014571, "relative_end": **********.014571, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.01476, "relative_start": 6.743294954299927, "end": **********.01476, "relative_end": **********.01476, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.016003, "relative_start": 6.744537830352783, "end": **********.016003, "relative_end": **********.016003, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.016334, "relative_start": 6.744868993759155, "end": **********.016334, "relative_end": **********.016334, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.017488, "relative_start": 6.746022939682007, "end": **********.017488, "relative_end": **********.017488, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.017719, "relative_start": 6.746253967285156, "end": **********.017719, "relative_end": **********.017719, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.018426, "relative_start": 6.746960878372192, "end": **********.018426, "relative_end": **********.018426, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.018731, "relative_start": 6.747266054153442, "end": **********.018731, "relative_end": **********.018731, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.018969, "relative_start": 6.747503995895386, "end": **********.018969, "relative_end": **********.018969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.019219, "relative_start": 6.747753858566284, "end": **********.019219, "relative_end": **********.019219, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.02039, "relative_start": 6.748924970626831, "end": **********.02039, "relative_end": **********.02039, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.020581, "relative_start": 6.749115943908691, "end": **********.020581, "relative_end": **********.020581, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.021681, "relative_start": 6.750216007232666, "end": **********.021681, "relative_end": **********.021681, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.021954, "relative_start": 6.750488996505737, "end": **********.021954, "relative_end": **********.021954, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.023062, "relative_start": 6.751596927642822, "end": **********.023062, "relative_end": **********.023062, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.023291, "relative_start": 6.751826047897339, "end": **********.023291, "relative_end": **********.023291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.023986, "relative_start": 6.75252103805542, "end": **********.023986, "relative_end": **********.023986, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.024289, "relative_start": 6.752823829650879, "end": **********.024289, "relative_end": **********.024289, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.024529, "relative_start": 6.753063917160034, "end": **********.024529, "relative_end": **********.024529, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.02478, "relative_start": 6.753314971923828, "end": **********.02478, "relative_end": **********.02478, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.025975, "relative_start": 6.754509925842285, "end": **********.025975, "relative_end": **********.025975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.026281, "relative_start": 6.754816055297852, "end": **********.026281, "relative_end": **********.026281, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.02782, "relative_start": 6.756355047225952, "end": **********.02782, "relative_end": **********.02782, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.028252, "relative_start": 6.756786823272705, "end": **********.028252, "relative_end": **********.028252, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.029615, "relative_start": 6.758149862289429, "end": **********.029615, "relative_end": **********.029615, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.03017, "relative_start": 6.758704900741577, "end": **********.03017, "relative_end": **********.03017, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.031153, "relative_start": 6.759687900543213, "end": **********.031153, "relative_end": **********.031153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.031975, "relative_start": 6.760509967803955, "end": **********.031975, "relative_end": **********.031975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.03244, "relative_start": 6.760974884033203, "end": **********.03244, "relative_end": **********.03244, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.032784, "relative_start": 6.761318922042847, "end": **********.032784, "relative_end": **********.032784, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.034299, "relative_start": 6.762833833694458, "end": **********.034299, "relative_end": **********.034299, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.034676, "relative_start": 6.763211011886597, "end": **********.034676, "relative_end": **********.034676, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.03618, "relative_start": 6.764714956283569, "end": **********.03618, "relative_end": **********.03618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.036658, "relative_start": 6.765192985534668, "end": **********.036658, "relative_end": **********.036658, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.037992, "relative_start": 6.766526937484741, "end": **********.037992, "relative_end": **********.037992, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.038387, "relative_start": 6.7669219970703125, "end": **********.038387, "relative_end": **********.038387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.039259, "relative_start": 6.767793893814087, "end": **********.039259, "relative_end": **********.039259, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.039645, "relative_start": 6.768179893493652, "end": **********.039645, "relative_end": **********.039645, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.039923, "relative_start": 6.768457889556885, "end": **********.039923, "relative_end": **********.039923, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.040262, "relative_start": 6.768796920776367, "end": **********.040262, "relative_end": **********.040262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.04168, "relative_start": 6.770215034484863, "end": **********.04168, "relative_end": **********.04168, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.042021, "relative_start": 6.7705559730529785, "end": **********.042021, "relative_end": **********.042021, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.043373, "relative_start": 6.7719080448150635, "end": **********.043373, "relative_end": **********.043373, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.043781, "relative_start": 6.772315979003906, "end": **********.043781, "relative_end": **********.043781, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.045151, "relative_start": 6.773685932159424, "end": **********.045151, "relative_end": **********.045151, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.045682, "relative_start": 6.774216890335083, "end": **********.045682, "relative_end": **********.045682, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.046802, "relative_start": 6.775336980819702, "end": **********.046802, "relative_end": **********.046802, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.047344, "relative_start": 6.77587890625, "end": **********.047344, "relative_end": **********.047344, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.047639, "relative_start": 6.776173830032349, "end": **********.047639, "relative_end": **********.047639, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.047908, "relative_start": 6.776443004608154, "end": **********.047908, "relative_end": **********.047908, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.049217, "relative_start": 6.777751922607422, "end": **********.049217, "relative_end": **********.049217, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.049464, "relative_start": 6.777998924255371, "end": **********.049464, "relative_end": **********.049464, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.050776, "relative_start": 6.779310941696167, "end": **********.050776, "relative_end": **********.050776, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.051153, "relative_start": 6.779687881469727, "end": **********.051153, "relative_end": **********.051153, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.052535, "relative_start": 6.781069993972778, "end": **********.052535, "relative_end": **********.052535, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.052888, "relative_start": 6.781422853469849, "end": **********.052888, "relative_end": **********.052888, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.053726, "relative_start": 6.782260894775391, "end": **********.053726, "relative_end": **********.053726, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.054233, "relative_start": 6.78276801109314, "end": **********.054233, "relative_end": **********.054233, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.054638, "relative_start": 6.783172845840454, "end": **********.054638, "relative_end": **********.054638, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.055109, "relative_start": 6.783643960952759, "end": **********.055109, "relative_end": **********.055109, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.056534, "relative_start": 6.785068988800049, "end": **********.056534, "relative_end": **********.056534, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.056804, "relative_start": 6.785338878631592, "end": **********.056804, "relative_end": **********.056804, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.057966, "relative_start": 6.786500930786133, "end": **********.057966, "relative_end": **********.057966, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.058255, "relative_start": 6.786789894104004, "end": **********.058255, "relative_end": **********.058255, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.059412, "relative_start": 6.787946939468384, "end": **********.059412, "relative_end": **********.059412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.059656, "relative_start": 6.788190841674805, "end": **********.059656, "relative_end": **********.059656, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.060405, "relative_start": 6.788939952850342, "end": **********.060405, "relative_end": **********.060405, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.060728, "relative_start": 6.789263010025024, "end": **********.060728, "relative_end": **********.060728, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.060974, "relative_start": 6.789508819580078, "end": **********.060974, "relative_end": **********.060974, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.061229, "relative_start": 6.789763927459717, "end": **********.061229, "relative_end": **********.061229, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.062507, "relative_start": 6.791041851043701, "end": **********.062507, "relative_end": **********.062507, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.062933, "relative_start": 6.791467905044556, "end": **********.062933, "relative_end": **********.062933, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.064449, "relative_start": 6.7929840087890625, "end": **********.064449, "relative_end": **********.064449, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.064832, "relative_start": 6.7933669090271, "end": **********.064832, "relative_end": **********.064832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.066145, "relative_start": 6.794679880142212, "end": **********.066145, "relative_end": **********.066145, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "start": **********.066651, "relative_start": 6.7951860427856445, "end": **********.066651, "relative_end": **********.066651, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.067399, "relative_start": 6.795933961868286, "end": **********.067399, "relative_end": **********.067399, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.068725, "relative_start": 6.797260046005249, "end": **********.068725, "relative_end": **********.068725, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.076554, "relative_start": 6.805088996887207, "end": **********.076554, "relative_end": **********.076554, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.077053, "relative_start": 6.805588006973267, "end": **********.077053, "relative_end": **********.077053, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.078495, "relative_start": 6.807029962539673, "end": **********.078495, "relative_end": **********.078495, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.078892, "relative_start": 6.807426929473877, "end": **********.078892, "relative_end": **********.078892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.08067, "relative_start": 6.809205055236816, "end": **********.08067, "relative_end": **********.08067, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.0816, "relative_start": 6.8101348876953125, "end": **********.0816, "relative_end": **********.0816, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.083811, "relative_start": 6.8123459815979, "end": **********.083811, "relative_end": **********.083811, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.084241, "relative_start": 6.8127758502960205, "end": **********.084241, "relative_end": **********.084241, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.084618, "relative_start": 6.813153028488159, "end": **********.084618, "relative_end": **********.084618, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.085887, "relative_start": 6.814421892166138, "end": **********.085887, "relative_end": **********.085887, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.087152, "relative_start": 6.815686941146851, "end": **********.087152, "relative_end": **********.087152, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.index", "start": **********.087474, "relative_start": 6.816009044647217, "end": **********.087474, "relative_end": **********.087474, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.090087, "relative_start": 6.818621873855591, "end": **********.090087, "relative_end": **********.090087, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.0913, "relative_start": 6.81983494758606, "end": **********.0913, "relative_end": **********.0913, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.091883, "relative_start": 6.820417881011963, "end": **********.091883, "relative_end": **********.091883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.modal.heading", "start": **********.092812, "relative_start": 6.821346998214722, "end": **********.092812, "relative_end": **********.092812, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.page.index", "start": **********.093504, "relative_start": 6.822038888931274, "end": **********.093504, "relative_end": **********.093504, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.header.index", "start": **********.09543, "relative_start": 6.823964834213257, "end": **********.09543, "relative_end": **********.09543, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.actions", "start": **********.096355, "relative_start": 6.824889898300171, "end": **********.096355, "relative_end": **********.096355, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.100167, "relative_start": 6.828701972961426, "end": **********.100167, "relative_end": **********.100167, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.100762, "relative_start": 6.829296827316284, "end": **********.100762, "relative_end": **********.100762, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.102194, "relative_start": 6.830729007720947, "end": **********.102194, "relative_end": **********.102194, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.103159, "relative_start": 6.831693887710571, "end": **********.103159, "relative_end": **********.103159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::1d99a7e4df9cb78eeaf464df03e7012b", "start": **********.104415, "relative_start": 6.832949876785278, "end": **********.104415, "relative_end": **********.104415, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.button.index", "start": **********.104849, "relative_start": 6.833384037017822, "end": **********.104849, "relative_end": **********.104849, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.106185, "relative_start": 6.834719896316528, "end": **********.106185, "relative_end": **********.106185, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.unsaved-action-changes-alert", "start": **********.106902, "relative_start": 6.835436820983887, "end": **********.106902, "relative_end": **********.106902, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::4943bc92ebba41e8b0e508149542e0ad", "start": **********.131537, "relative_start": 6.860071897506714, "end": **********.131537, "relative_end": **********.131537, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.index", "start": **********.132636, "relative_start": 6.861171007156372, "end": **********.132636, "relative_end": **********.132636, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.topbar.index", "start": **********.150273, "relative_start": 6.87880802154541, "end": **********.150273, "relative_end": **********.150273, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.151891, "relative_start": 6.880425930023193, "end": **********.151891, "relative_end": **********.151891, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.153023, "relative_start": 6.881557941436768, "end": **********.153023, "relative_end": **********.153023, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.153491, "relative_start": 6.882025957107544, "end": **********.153491, "relative_end": **********.153491, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.154733, "relative_start": 6.883267879486084, "end": **********.154733, "relative_end": **********.154733, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.global-search.index", "start": **********.156688, "relative_start": 6.885222911834717, "end": **********.156688, "relative_end": **********.156688, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.global-search.field", "start": **********.157364, "relative_start": 6.88589882850647, "end": **********.157364, "relative_end": **********.157364, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.index", "start": **********.159389, "relative_start": 6.887923955917358, "end": **********.159389, "relative_end": **********.159389, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.input.wrapper", "start": **********.160561, "relative_start": 6.889096021652222, "end": **********.160561, "relative_end": **********.160561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.162159, "relative_start": 6.89069390296936, "end": **********.162159, "relative_end": **********.162159, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.loading-indicator", "start": **********.162926, "relative_start": 6.89146089553833, "end": **********.162926, "relative_end": **********.162926, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::fc6af43a80d1f8feb6964b2b41596895", "start": **********.165128, "relative_start": 6.893662929534912, "end": **********.165128, "relative_end": **********.165128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.language-switcher", "start": **********.166541, "relative_start": 6.895076036453247, "end": **********.166541, "relative_end": **********.166541, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.user-menu", "start": **********.168648, "relative_start": 6.897182941436768, "end": **********.168648, "relative_end": **********.168648, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.avatar.user", "start": **********.170089, "relative_start": 6.898623943328857, "end": **********.170089, "relative_end": **********.170089, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.avatar", "start": **********.173295, "relative_start": 6.901829957962036, "end": **********.173295, "relative_end": **********.173295, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.header", "start": **********.173807, "relative_start": 6.902341842651367, "end": **********.173807, "relative_end": **********.173807, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.174298, "relative_start": 6.902832984924316, "end": **********.174298, "relative_end": **********.174298, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.index", "start": **********.174944, "relative_start": 6.9034788608551025, "end": **********.174944, "relative_end": **********.174944, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.17538, "relative_start": 6.903914928436279, "end": **********.17538, "relative_end": **********.17538, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.175748, "relative_start": 6.904283046722412, "end": **********.175748, "relative_end": **********.175748, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.176173, "relative_start": 6.904707908630371, "end": **********.176173, "relative_end": **********.176173, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.176459, "relative_start": 6.904994010925293, "end": **********.176459, "relative_end": **********.176459, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.theme-switcher.button", "start": **********.176824, "relative_start": 6.9053590297698975, "end": **********.176824, "relative_end": **********.176824, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.1771, "relative_start": 6.905634880065918, "end": **********.1771, "relative_end": **********.1771, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.177585, "relative_start": 6.9061198234558105, "end": **********.177585, "relative_end": **********.177585, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.item", "start": **********.178112, "relative_start": 6.906646966934204, "end": **********.178112, "relative_end": **********.178112, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.179428, "relative_start": 6.907963037490845, "end": **********.179428, "relative_end": **********.179428, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.list.index", "start": **********.180007, "relative_start": 6.908541917800903, "end": **********.180007, "relative_end": **********.180007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.dropdown.index", "start": **********.18024, "relative_start": 6.9087748527526855, "end": **********.18024, "relative_end": **********.18024, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.index", "start": **********.181266, "relative_start": 6.909801006317139, "end": **********.181266, "relative_end": **********.181266, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.logo", "start": **********.186815, "relative_start": 6.915349960327148, "end": **********.186815, "relative_end": **********.186815, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.18761, "relative_start": 6.916144847869873, "end": **********.18761, "relative_end": **********.18761, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.189621, "relative_start": 6.918155908584595, "end": **********.189621, "relative_end": **********.189621, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.190892, "relative_start": 6.919426918029785, "end": **********.190892, "relative_end": **********.190892, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.191832, "relative_start": 6.920367002487183, "end": **********.191832, "relative_end": **********.191832, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.193128, "relative_start": 6.921663045883179, "end": **********.193128, "relative_end": **********.193128, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.194286, "relative_start": 6.922821044921875, "end": **********.194286, "relative_end": **********.194286, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.194928, "relative_start": 6.923462867736816, "end": **********.194928, "relative_end": **********.194928, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.195667, "relative_start": 6.924201965332031, "end": **********.195667, "relative_end": **********.195667, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.196401, "relative_start": 6.924935817718506, "end": **********.196401, "relative_end": **********.196401, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.197591, "relative_start": 6.926126003265381, "end": **********.197591, "relative_end": **********.197591, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.199264, "relative_start": 6.9277989864349365, "end": **********.199264, "relative_end": **********.199264, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.199845, "relative_start": 6.928380012512207, "end": **********.199845, "relative_end": **********.199845, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.200653, "relative_start": 6.929188013076782, "end": **********.200653, "relative_end": **********.200653, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.201291, "relative_start": 6.929826021194458, "end": **********.201291, "relative_end": **********.201291, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.201959, "relative_start": 6.9304938316345215, "end": **********.201959, "relative_end": **********.201959, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.202346, "relative_start": 6.930881023406982, "end": **********.202346, "relative_end": **********.202346, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.203315, "relative_start": 6.931849956512451, "end": **********.203315, "relative_end": **********.203315, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.204277, "relative_start": 6.932811975479126, "end": **********.204277, "relative_end": **********.204277, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.205077, "relative_start": 6.933611869812012, "end": **********.205077, "relative_end": **********.205077, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.205666, "relative_start": 6.934201002120972, "end": **********.205666, "relative_end": **********.205666, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.206394, "relative_start": 6.934928894042969, "end": **********.206394, "relative_end": **********.206394, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.206983, "relative_start": 6.935518026351929, "end": **********.206983, "relative_end": **********.206983, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.20814, "relative_start": 6.9366748332977295, "end": **********.20814, "relative_end": **********.20814, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.209198, "relative_start": 6.937732934951782, "end": **********.209198, "relative_end": **********.209198, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.209619, "relative_start": 6.938153982162476, "end": **********.209619, "relative_end": **********.209619, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.210391, "relative_start": 6.9389259815216064, "end": **********.210391, "relative_end": **********.210391, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.211303, "relative_start": 6.93983793258667, "end": **********.211303, "relative_end": **********.211303, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.212425, "relative_start": 6.940959930419922, "end": **********.212425, "relative_end": **********.212425, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.213447, "relative_start": 6.94198203086853, "end": **********.213447, "relative_end": **********.213447, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.213883, "relative_start": 6.942417860031128, "end": **********.213883, "relative_end": **********.213883, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.214526, "relative_start": 6.943060874938965, "end": **********.214526, "relative_end": **********.214526, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.215091, "relative_start": 6.9436259269714355, "end": **********.215091, "relative_end": **********.215091, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.215788, "relative_start": 6.944322824478149, "end": **********.215788, "relative_end": **********.215788, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.216339, "relative_start": 6.944874048233032, "end": **********.216339, "relative_end": **********.216339, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.217088, "relative_start": 6.94562292098999, "end": **********.217088, "relative_end": **********.217088, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.21755, "relative_start": 6.946084976196289, "end": **********.21755, "relative_end": **********.21755, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.218272, "relative_start": 6.946806907653809, "end": **********.218272, "relative_end": **********.218272, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.219093, "relative_start": 6.947628021240234, "end": **********.219093, "relative_end": **********.219093, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.220188, "relative_start": 6.948722839355469, "end": **********.220188, "relative_end": **********.220188, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.221226, "relative_start": 6.949760913848877, "end": **********.221226, "relative_end": **********.221226, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.221756, "relative_start": 6.95029091835022, "end": **********.221756, "relative_end": **********.221756, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.222458, "relative_start": 6.950992822647095, "end": **********.222458, "relative_end": **********.222458, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.223031, "relative_start": 6.951565980911255, "end": **********.223031, "relative_end": **********.223031, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.223736, "relative_start": 6.952270984649658, "end": **********.223736, "relative_end": **********.223736, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.224387, "relative_start": 6.9529218673706055, "end": **********.224387, "relative_end": **********.224387, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.225179, "relative_start": 6.953713893890381, "end": **********.225179, "relative_end": **********.225179, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.226347, "relative_start": 6.954881906509399, "end": **********.226347, "relative_end": **********.226347, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.227561, "relative_start": 6.956095933914185, "end": **********.227561, "relative_end": **********.227561, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.228847, "relative_start": 6.957381963729858, "end": **********.228847, "relative_end": **********.228847, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.22947, "relative_start": 6.958004951477051, "end": **********.22947, "relative_end": **********.22947, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.230183, "relative_start": 6.9587178230285645, "end": **********.230183, "relative_end": **********.230183, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.23084, "relative_start": 6.959374904632568, "end": **********.23084, "relative_end": **********.23084, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.231628, "relative_start": 6.960162878036499, "end": **********.231628, "relative_end": **********.231628, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.232646, "relative_start": 6.961180925369263, "end": **********.232646, "relative_end": **********.232646, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.234486, "relative_start": 6.9630210399627686, "end": **********.234486, "relative_end": **********.234486, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.236611, "relative_start": 6.965145826339722, "end": **********.236611, "relative_end": **********.236611, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.237766, "relative_start": 6.966300964355469, "end": **********.237766, "relative_end": **********.237766, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.238969, "relative_start": 6.967504024505615, "end": **********.238969, "relative_end": **********.238969, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.239568, "relative_start": 6.968102931976318, "end": **********.239568, "relative_end": **********.239568, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.24028, "relative_start": 6.968814849853516, "end": **********.24028, "relative_end": **********.24028, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.group", "start": **********.241228, "relative_start": 6.9697630405426025, "end": **********.241228, "relative_end": **********.241228, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon-button", "start": **********.24249, "relative_start": 6.971024990081787, "end": **********.24249, "relative_end": **********.24249, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.243583, "relative_start": 6.972117900848389, "end": **********.243583, "relative_end": **********.243583, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.244069, "relative_start": 6.972604036331177, "end": **********.244069, "relative_end": **********.244069, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.244809, "relative_start": 6.973343849182129, "end": **********.244809, "relative_end": **********.244809, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.sidebar.item", "start": **********.245729, "relative_start": 6.974263906478882, "end": **********.245729, "relative_end": **********.245729, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::components.icon", "start": **********.246578, "relative_start": 6.9751129150390625, "end": **********.246578, "relative_end": **********.246578, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament-panels::components.layout.base", "start": **********.248166, "relative_start": 6.976701021194458, "end": **********.248166, "relative_end": **********.248166, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.250007, "relative_start": 6.978541851043701, "end": **********.250007, "relative_end": **********.250007, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::27df4909b5b8fbf02f3a65ba2f758414", "start": **********.251501, "relative_start": 6.980036020278931, "end": **********.251501, "relative_end": **********.251501, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: filament::assets", "start": **********.258155, "relative_start": 6.986690044403076, "end": **********.258155, "relative_end": **********.258155, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: __components::97c5322e947dbf60ae5ca480928dd519", "start": **********.259019, "relative_start": 6.987553834915161, "end": **********.259019, "relative_end": **********.259019, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "Preparing Response", "start": **********.264371, "relative_start": 6.992905855178833, "end": **********.264586, "relative_end": **********.264586, "duration": 0.00021505355834960938, "duration_str": "215μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.267655, "relative_start": 6.996189832687378, "end": **********.267895, "relative_end": **********.267895, "duration": 0.00024008750915527344, "duration_str": "240μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": 50484136, "peak_usage_str": "48MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 336, "nb_templates": 336, "templates": [{"name": "1x filament.admin.resources.menu-item-resource.pages.manage-menu-items", "param_count": null, "params": [], "start": **********.443871, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.phpfilament.admin.resources.menu-item-resource.pages.manage-menu-items", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ffilament%2Fadmin%2Fresources%2Fmenu-item-resource%2Fpages%2Fmanage-menu-items.blade.php&line=1", "ajax": false, "filename": "manage-menu-items.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament.admin.resources.menu-item-resource.pages.manage-menu-items"}, {"name": "133x filament::components.icon", "param_count": null, "params": [], "start": **********.782394, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon.blade.phpfilament::components.icon", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon.blade.php&line=1", "ajax": false, "filename": "icon.blade.php", "line": "?"}, "render_count": 133, "name_original": "filament::components.icon"}, {"name": "63x filament::components.button.index", "param_count": null, "params": [], "start": **********.784266, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/button/index.blade.phpfilament::components.button.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fbutton%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 63, "name_original": "filament::components.button.index"}, {"name": "45x filament::components.loading-indicator", "param_count": null, "params": [], "start": **********.786672, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/loading-indicator.blade.phpfilament::components.loading-indicator", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Floading-indicator.blade.php&line=1", "ajax": false, "filename": "loading-indicator.blade.php", "line": "?"}, "render_count": 45, "name_original": "filament::components.loading-indicator"}, {"name": "17x filament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "param_count": null, "params": [], "start": **********.963986, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/partials/wordpress-menu-item.blade.phpfilament.admin.resources.menu-item-resource.partials.wordpress-menu-item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ffilament%2Fadmin%2Fresources%2Fmenu-item-resource%2Fpartials%2Fwordpress-menu-item.blade.php&line=1", "ajax": false, "filename": "wordpress-menu-item.blade.php", "line": "?"}, "render_count": 17, "name_original": "filament.admin.resources.menu-item-resource.partials.wordpress-menu-item"}, {"name": "1x filament::components.modal.index", "param_count": null, "params": [], "start": **********.087459, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/index.blade.phpfilament::components.modal.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.modal.index"}, {"name": "11x filament::components.icon-button", "param_count": null, "params": [], "start": **********.090068, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/icon-button.blade.phpfilament::components.icon-button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Ficon-button.blade.php&line=1", "ajax": false, "filename": "icon-button.blade.php", "line": "?"}, "render_count": 11, "name_original": "filament::components.icon-button"}, {"name": "1x filament::components.modal.heading", "param_count": null, "params": [], "start": **********.092796, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/modal/heading.blade.phpfilament::components.modal.heading", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fmodal%2Fheading.blade.php&line=1", "ajax": false, "filename": "heading.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.modal.heading"}, {"name": "1x filament-panels::components.page.index", "param_count": null, "params": [], "start": **********.093487, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/page/index.blade.phpfilament-panels::components.page.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fpage%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.page.index"}, {"name": "1x filament-panels::components.header.index", "param_count": null, "params": [], "start": **********.09541, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/header/index.blade.phpfilament-panels::components.header.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fheader%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.header.index"}, {"name": "1x filament::components.actions", "param_count": null, "params": [], "start": **********.096335, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/actions.blade.phpfilament::components.actions", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Factions.blade.php&line=1", "ajax": false, "filename": "actions.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.actions"}, {"name": "2x __components::1d99a7e4df9cb78eeaf464df03e7012b", "param_count": null, "params": [], "start": **********.100148, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/1d99a7e4df9cb78eeaf464df03e7012b.blade.php__components::1d99a7e4df9cb78eeaf464df03e7012b", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F1d99a7e4df9cb78eeaf464df03e7012b.blade.php&line=1", "ajax": false, "filename": "1d99a7e4df9cb78eeaf464df03e7012b.blade.php", "line": "?"}, "render_count": 2, "name_original": "__components::1d99a7e4df9cb78eeaf464df03e7012b"}, {"name": "1x filament-panels::components.unsaved-action-changes-alert", "param_count": null, "params": [], "start": **********.106885, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/unsaved-action-changes-alert.blade.phpfilament-panels::components.unsaved-action-changes-alert", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Funsaved-action-changes-alert.blade.php&line=1", "ajax": false, "filename": "unsaved-action-changes-alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.unsaved-action-changes-alert"}, {"name": "1x __components::4943bc92ebba41e8b0e508149542e0ad", "param_count": null, "params": [], "start": **********.131513, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/4943bc92ebba41e8b0e508149542e0ad.blade.php__components::4943bc92ebba41e8b0e508149542e0ad", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F4943bc92ebba41e8b0e508149542e0ad.blade.php&line=1", "ajax": false, "filename": "4943bc92ebba41e8b0e508149542e0ad.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::4943bc92ebba41e8b0e508149542e0ad"}, {"name": "1x filament-panels::components.layout.index", "param_count": null, "params": [], "start": **********.132617, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/layout/index.blade.phpfilament-panels::components.layout.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.index"}, {"name": "1x filament-panels::components.topbar.index", "param_count": null, "params": [], "start": **********.150256, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/topbar/index.blade.phpfilament-panels::components.topbar.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftopbar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.topbar.index"}, {"name": "1x filament-panels::components.global-search.index", "param_count": null, "params": [], "start": **********.156672, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/global-search/index.blade.phpfilament-panels::components.global-search.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fglobal-search%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.global-search.index"}, {"name": "1x filament-panels::components.global-search.field", "param_count": null, "params": [], "start": **********.157346, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/global-search/field.blade.phpfilament-panels::components.global-search.field", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fglobal-search%2Ffield.blade.php&line=1", "ajax": false, "filename": "field.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.global-search.field"}, {"name": "1x filament::components.input.index", "param_count": null, "params": [], "start": **********.159365, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/index.blade.phpfilament::components.input.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.input.index"}, {"name": "1x filament::components.input.wrapper", "param_count": null, "params": [], "start": **********.160521, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/input/wrapper.blade.phpfilament::components.input.wrapper", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Finput%2Fwrapper.blade.php&line=1", "ajax": false, "filename": "wrapper.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.input.wrapper"}, {"name": "1x __components::fc6af43a80d1f8feb6964b2b41596895", "param_count": null, "params": [], "start": **********.165109, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/fc6af43a80d1f8feb6964b2b41596895.blade.php__components::fc6af43a80d1f8feb6964b2b41596895", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Ffc6af43a80d1f8feb6964b2b41596895.blade.php&line=1", "ajax": false, "filename": "fc6af43a80d1f8feb6964b2b41596895.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::fc6af43a80d1f8feb6964b2b41596895"}, {"name": "1x livewire.language-switcher", "param_count": null, "params": [], "start": **********.166524, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/language-switcher.blade.phplivewire.language-switcher", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Flanguage-switcher.blade.php&line=1", "ajax": false, "filename": "language-switcher.blade.php", "line": "?"}, "render_count": 1, "name_original": "livewire.language-switcher"}, {"name": "1x filament-panels::components.user-menu", "param_count": null, "params": [], "start": **********.168632, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/user-menu.blade.phpfilament-panels::components.user-menu", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fuser-menu.blade.php&line=1", "ajax": false, "filename": "user-menu.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.user-menu"}, {"name": "1x filament-panels::components.avatar.user", "param_count": null, "params": [], "start": **********.170073, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/avatar/user.blade.phpfilament-panels::components.avatar.user", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Favatar%2Fuser.blade.php&line=1", "ajax": false, "filename": "user.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.avatar.user"}, {"name": "1x filament::components.avatar", "param_count": null, "params": [], "start": **********.173278, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/avatar.blade.phpfilament::components.avatar", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Favatar.blade.php&line=1", "ajax": false, "filename": "avatar.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.avatar"}, {"name": "1x filament::components.dropdown.header", "param_count": null, "params": [], "start": **********.173794, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/header.blade.phpfilament::components.dropdown.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.header"}, {"name": "1x filament-panels::components.theme-switcher.index", "param_count": null, "params": [], "start": **********.174931, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/theme-switcher/index.blade.phpfilament-panels::components.theme-switcher.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.theme-switcher.index"}, {"name": "3x filament-panels::components.theme-switcher.button", "param_count": null, "params": [], "start": **********.175367, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/theme-switcher/button.blade.phpfilament-panels::components.theme-switcher.button", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Ftheme-switcher%2Fbutton.blade.php&line=1", "ajax": false, "filename": "button.blade.php", "line": "?"}, "render_count": 3, "name_original": "filament-panels::components.theme-switcher.button"}, {"name": "2x filament::components.dropdown.list.index", "param_count": null, "params": [], "start": **********.17757, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/list/index.blade.phpfilament::components.dropdown.list.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::components.dropdown.list.index"}, {"name": "1x filament::components.dropdown.list.item", "param_count": null, "params": [], "start": **********.178099, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/list/item.blade.phpfilament::components.dropdown.list.item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Flist%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.list.item"}, {"name": "1x filament::components.dropdown.index", "param_count": null, "params": [], "start": **********.180228, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/components/dropdown/index.blade.phpfilament::components.dropdown.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fcomponents%2Fdropdown%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament::components.dropdown.index"}, {"name": "1x filament-panels::components.sidebar.index", "param_count": null, "params": [], "start": **********.181234, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/index.blade.phpfilament-panels::components.sidebar.index", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Findex.blade.php&line=1", "ajax": false, "filename": "index.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.sidebar.index"}, {"name": "1x filament-panels::components.logo", "param_count": null, "params": [], "start": **********.1868, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/logo.blade.phpfilament-panels::components.logo", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flogo.blade.php&line=1", "ajax": false, "filename": "logo.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.logo"}, {"name": "9x filament-panels::components.sidebar.group", "param_count": null, "params": [], "start": **********.187596, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/group.blade.phpfilament-panels::components.sidebar.group", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fgroup.blade.php&line=1", "ajax": false, "filename": "group.blade.php", "line": "?"}, "render_count": 9, "name_original": "filament-panels::components.sidebar.group"}, {"name": "21x filament-panels::components.sidebar.item", "param_count": null, "params": [], "start": **********.1896, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/sidebar/item.blade.phpfilament-panels::components.sidebar.item", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Fsidebar%2Fitem.blade.php&line=1", "ajax": false, "filename": "item.blade.php", "line": "?"}, "render_count": 21, "name_original": "filament-panels::components.sidebar.item"}, {"name": "1x filament-panels::components.layout.base", "param_count": null, "params": [], "start": **********.248146, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament-panels/components/layout/base.blade.phpfilament-panels::components.layout.base", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament-panels%2Fcomponents%2Flayout%2Fbase.blade.php&line=1", "ajax": false, "filename": "base.blade.php", "line": "?"}, "render_count": 1, "name_original": "filament-panels::components.layout.base"}, {"name": "2x filament::assets", "param_count": null, "params": [], "start": **********.249989, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/filament/assets.blade.phpfilament::assets", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Ffilament%2Fassets.blade.php&line=1", "ajax": false, "filename": "assets.blade.php", "line": "?"}, "render_count": 2, "name_original": "filament::assets"}, {"name": "1x __components::27df4909b5b8fbf02f3a65ba2f758414", "param_count": null, "params": [], "start": **********.251484, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/27df4909b5b8fbf02f3a65ba2f758414.blade.php__components::27df4909b5b8fbf02f3a65ba2f758414", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F27df4909b5b8fbf02f3a65ba2f758414.blade.php&line=1", "ajax": false, "filename": "27df4909b5b8fbf02f3a65ba2f758414.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::27df4909b5b8fbf02f3a65ba2f758414"}, {"name": "1x __components::97c5322e947dbf60ae5ca480928dd519", "param_count": null, "params": [], "start": **********.258996, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\storage\\framework\\views/97c5322e947dbf60ae5ca480928dd519.blade.php__components::97c5322e947dbf60ae5ca480928dd519", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2F97c5322e947dbf60ae5ca480928dd519.blade.php&line=1", "ajax": false, "filename": "97c5322e947dbf60ae5ca480928dd519.blade.php", "line": "?"}, "render_count": 1, "name_original": "__components::97c5322e947dbf60ae5ca480928dd519"}]}, "queries": {"count": 176, "nb_statements": 176, "nb_visible_statements": 176, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.11452000000000002, "accumulated_duration_str": "115ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "# Query soft limit for Debugbar is reached after 100 queries, additional 76 queries only show the query. Limits can be raised in the config (debugbar.options.db.soft_limit)", "type": "info"}, {"sql": "select * from `users` where `id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 180}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Http\\Middleware\\Authenticate.php", "line": 19}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 62}], "start": **********.142371, "duration": 0.00268, "duration_str": "2.68ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 2.34}, {"sql": "select * from `menus` where `menus`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 177}, {"index": 22, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 119}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.181343, "duration": 0.0035, "duration_str": "3.5ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:177", "source": {"index": 21, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 177}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=177", "ajax": false, "filename": "ManageMenuItems.php", "line": "177"}, "connection": "auvista", "explain": null, "start_percent": 2.34, "width_percent": 3.056}, {"sql": "select * from `permissions`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 16, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 17, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.2179642, "duration": 0.00204, "duration_str": "2.04ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 15, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 5.396, "width_percent": 1.781}, {"sql": "select `roles`.*, `role_has_permissions`.`permission_id` as `pivot_permission_id`, `role_has_permissions`.`role_id` as `pivot_role_id` from `roles` inner join `role_has_permissions` on `roles`.`id` = `role_has_permissions`.`role_id` where `role_has_permissions`.`permission_id` in (1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 31, 32, 33, 34, 35, 36, 37, 38, 39, 40)", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 313}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 198}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/Repository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\Repository.php", "line": 427}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 197}], "start": **********.227751, "duration": 0.0018700000000000001, "duration_str": "1.87ms", "memory": 0, "memory_str": null, "filename": "PermissionRegistrar.php:276", "source": {"index": 19, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 276}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FPermissionRegistrar.php&line=276", "ajax": false, "filename": "PermissionRegistrar.php", "line": "276"}, "connection": "auvista", "explain": null, "start_percent": 7.178, "width_percent": 1.633}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.364382, "duration": 0.004059999999999999, "duration_str": "4.06ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 8.811, "width_percent": 3.545}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.369808, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 12.356, "width_percent": 0.533}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.37121, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 12.889, "width_percent": 0.384}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.37251, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 13.273, "width_percent": 0.603}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.374006, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 13.875, "width_percent": 0.603}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.3754282, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 14.478, "width_percent": 0.384}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.376621, "duration": 0.0004, "duration_str": "400μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 14.862, "width_percent": 0.349}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.377781, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 15.211, "width_percent": 0.358}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.37906, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 15.569, "width_percent": 0.943}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.3809268, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 16.512, "width_percent": 0.506}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.382121, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 17.019, "width_percent": 0.279}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.382996, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 17.298, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.383862, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 17.534, "width_percent": 0.262}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.3847961, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 17.796, "width_percent": 0.262}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.385845, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 18.058, "width_percent": 0.524}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.3875072, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 18.582, "width_percent": 0.55}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.388892, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 19.132, "width_percent": 0.454}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.390163, "duration": 0.00041999999999999996, "duration_str": "420μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 19.586, "width_percent": 0.367}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "start": **********.397171, "duration": 0.00354, "duration_str": "3.54ms", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 19.953, "width_percent": 3.091}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.4025419, "duration": 0.00128, "duration_str": "1.28ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 23.044, "width_percent": 1.118}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.407132, "duration": 0.0019299999999999999, "duration_str": "1.93ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 24.162, "width_percent": 1.685}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.410643, "duration": 0.00185, "duration_str": "1.85ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 25.847, "width_percent": 1.615}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.416565, "duration": 0.00492, "duration_str": "4.92ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 27.462, "width_percent": 4.296}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": null, "name": "vendor/filament/filament/src/Pages/BasePage.php", "file": "H:\\laragon\\www\\auvista\\vendor\\filament\\filament\\src\\Pages\\BasePage.php", "line": 53}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 95}], "start": **********.429389, "duration": 0.00461, "duration_str": "4.61ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 31.759, "width_percent": 4.025}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.796645, "duration": 0.00125, "duration_str": "1.25ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 35.784, "width_percent": 1.092}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.799043, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 36.876, "width_percent": 1.353}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.801341, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.229, "width_percent": 0.41}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.802565, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 38.64, "width_percent": 0.41}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.80394, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.05, "width_percent": 0.541}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.805416, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 39.591, "width_percent": 0.428}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.806802, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.019, "width_percent": 0.454}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.808176, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.473, "width_percent": 0.463}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.809598, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 40.936, "width_percent": 0.463}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.811041, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 41.399, "width_percent": 0.559}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.812566, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 41.958, "width_percent": 0.655}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.814066, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 42.613, "width_percent": 0.375}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.815409, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 42.988, "width_percent": 0.332}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.816756, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 43.32, "width_percent": 0.629}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.818243, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 43.949, "width_percent": 0.332}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.819362, "duration": 0.00043, "duration_str": "430μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 44.28, "width_percent": 0.375}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.82052, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 44.656, "width_percent": 0.288}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8214931, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 44.944, "width_percent": 0.279}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.822488, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 45.224, "width_percent": 0.306}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.823591, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 45.529, "width_percent": 0.515}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8254468, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 46.044, "width_percent": 0.489}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.827383, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 46.533, "width_percent": 0.559}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8289, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 47.092, "width_percent": 0.472}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 337}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.830668, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 47.564, "width_percent": 0.576}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8383658, "duration": 0.00147, "duration_str": "1.47ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 48.14, "width_percent": 1.284}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.841212, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 49.424, "width_percent": 0.472}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.84287, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 49.895, "width_percent": 0.646}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.844674, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 50.541, "width_percent": 0.716}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.846451, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 51.257, "width_percent": 0.454}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.847733, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 51.711, "width_percent": 0.428}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.849195, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 52.139, "width_percent": 0.358}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.850407, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 52.497, "width_percent": 0.323}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.851601, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 52.82, "width_percent": 0.611}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.853079, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 53.432, "width_percent": 0.559}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8544781, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 53.991, "width_percent": 0.279}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8555171, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 54.27, "width_percent": 0.384}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.856707, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 54.654, "width_percent": 0.402}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.858072, "duration": 0.00108, "duration_str": "1.08ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 55.056, "width_percent": 0.943}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.859991, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 55.999, "width_percent": 0.454}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.861386, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 56.453, "width_percent": 0.428}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8630521, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 56.881, "width_percent": 0.699}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.864833, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 57.579, "width_percent": 0.847}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.866591, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 58.426, "width_percent": 0.472}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8678012, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 58.898, "width_percent": 0.41}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.869315, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 59.308, "width_percent": 0.498}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.870714, "duration": 0.00038, "duration_str": "380μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 59.806, "width_percent": 0.332}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.871839, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 60.138, "width_percent": 0.629}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 510}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.873563, "duration": 0.0009, "duration_str": "900μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 60.767, "width_percent": 0.786}, {"sql": "select * from `menu_items` where `menu_id` = 5 order by `order` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.8842971, "duration": 0.00099, "duration_str": "990μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:381", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 381}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=381", "ajax": false, "filename": "ManageMenuItems.php", "line": "381"}, "connection": "auvista", "explain": null, "start_percent": 61.553, "width_percent": 0.864}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 17 limit 1", "type": "query", "params": [], "bindings": [17], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.886522, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 62.417, "width_percent": 0.603}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 19 limit 1", "type": "query", "params": [], "bindings": [19], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8881168, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 63.02, "width_percent": 0.533}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 20 limit 1", "type": "query", "params": [], "bindings": [20], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8895981, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 63.552, "width_percent": 0.41}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 18 limit 1", "type": "query", "params": [], "bindings": [18], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.891087, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 63.963, "width_percent": 0.594}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 21 limit 1", "type": "query", "params": [], "bindings": [21], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8926091, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 64.556, "width_percent": 0.384}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 24 limit 1", "type": "query", "params": [], "bindings": [24], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8938138, "duration": 0.00039, "duration_str": "390μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 64.941, "width_percent": 0.341}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 22 limit 1", "type": "query", "params": [], "bindings": [22], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.894954, "duration": 0.00041, "duration_str": "410μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 65.281, "width_percent": 0.358}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 25 limit 1", "type": "query", "params": [], "bindings": [25], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8960202, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 65.639, "width_percent": 0.253}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 28 limit 1", "type": "query", "params": [], "bindings": [28], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8968692, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 65.892, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 23 limit 1", "type": "query", "params": [], "bindings": [23], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.897711, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 66.128, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 26 limit 1", "type": "query", "params": [], "bindings": [26], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.8985312, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 66.364, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 29 limit 1", "type": "query", "params": [], "bindings": [29], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.899344, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 66.6, "width_percent": 0.253}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 27 limit 1", "type": "query", "params": [], "bindings": [27], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.900175, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 66.853, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 30 limit 1", "type": "query", "params": [], "bindings": [30], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.9009972, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 67.097, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 31 limit 1", "type": "query", "params": [], "bindings": [31], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.9018192, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 67.342, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 32 limit 1", "type": "query", "params": [], "bindings": [32], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.9026392, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 67.578, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = 65 limit 1", "type": "query", "params": [], "bindings": [65], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.90345, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:384", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 384}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=384", "ajax": false, "filename": "ManageMenuItems.php", "line": "384"}, "connection": "auvista", "explain": null, "start_percent": 67.813, "width_percent": 0.244}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 391}, {"index": 32, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 33, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}], "start": **********.9042761, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 68.058, "width_percent": 0.262}, {"sql": "select * from `posts` where `posts`.`id` = 3 limit 1", "type": "query", "params": [], "bindings": [3], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, {"index": 25, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 382}, {"index": 26, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 28, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}], "start": **********.905083, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:402", "source": {"index": 20, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 402}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=402", "ajax": false, "filename": "ManageMenuItems.php", "line": "402"}, "connection": "auvista", "explain": null, "start_percent": 68.32, "width_percent": 0.576}, {"sql": "select `id`, `title` from `static_pages` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.9068022, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:444", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=444", "ajax": false, "filename": "ManageMenuItems.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 68.896, "width_percent": 0.55}, {"sql": "select `id`, `title` from `posts` where `status` = 'published' order by `title` asc", "type": "query", "params": [], "bindings": ["published"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.908752, "duration": 0.00124, "duration_str": "1.24ms", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:449", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 449}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=449", "ajax": false, "filename": "ManageMenuItems.php", "line": "449"}, "connection": "auvista", "explain": null, "start_percent": 69.446, "width_percent": 1.083}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.911543, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:453", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 453}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=453", "ajax": false, "filename": "ManageMenuItems.php", "line": "453"}, "connection": "auvista", "explain": null, "start_percent": 70.529, "width_percent": 0.62}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, {"index": 16, "namespace": "view", "name": "filament.admin.resources.menu-item-resource.pages.manage-menu-items", "file": "H:\\laragon\\www\\auvista\\resources\\views/filament/admin/resources/menu-item-resource/pages/manage-menu-items.blade.php", "line": 771}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}], "start": **********.913222, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "ManageMenuItems.php:457", "source": {"index": 15, "namespace": null, "name": "app/Filament/Resources/MenuItemResource/Pages/ManageMenuItems.php", "file": "H:\\laragon\\www\\auvista\\app\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems.php", "line": 457}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FFilament%2FResources%2FMenuItemResource%2FPages%2FManageMenuItems.php&line=457", "ajax": false, "filename": "ManageMenuItems.php", "line": "457"}, "connection": "auvista", "explain": null, "start_percent": 71.149, "width_percent": 0.262}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.917656, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 71.411, "width_percent": 0.62}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9186602, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.031, "width_percent": 0.253}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9191492, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.284, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9196079, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.52, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9200552, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.747, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.920525, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 72.992, "width_percent": 0.41}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.921206, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.402, "width_percent": 0.253}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.921695, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.655, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.922184, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 73.9, "width_percent": 0.454}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.922929, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.354, "width_percent": 0.262}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.923417, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.616, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.923875, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 74.852, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.924329, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.087, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9247892, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.332, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.92527, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.576, "width_percent": 0.271}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.92576, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 75.847, "width_percent": 0.253}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9262302, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.1, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.926698, "duration": 0.00031, "duration_str": "310μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.345, "width_percent": 0.271}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927193, "duration": 0.00033, "duration_str": "330μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.615, "width_percent": 0.288}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.927725, "duration": 0.00035, "duration_str": "350μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 76.904, "width_percent": 0.306}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.928647, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.209, "width_percent": 0.314}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929308, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.524, "width_percent": 0.279}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.929845, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 77.803, "width_percent": 0.253}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.930458, "duration": 0.00065, "duration_str": "650μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.056, "width_percent": 0.568}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.934187, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 78.624, "width_percent": 0.506}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935051, "duration": 0.00029, "duration_str": "290μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 79.13, "width_percent": 0.253}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.935535, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 79.384, "width_percent": 0.445}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9362369, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 79.829, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.936683, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.056, "width_percent": 0.655}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.937726, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 80.711, "width_percent": 0.699}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.939085, "duration": 0.00123, "duration_str": "1.23ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 81.409, "width_percent": 1.074}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.940763, "duration": 0.00101, "duration_str": "1.01ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 82.483, "width_percent": 0.882}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.942086, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 83.365, "width_percent": 0.472}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.942874, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 83.837, "width_percent": 0.262}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.943367, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.099, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9438138, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.326, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9442618, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.553, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.94471, "duration": 0.00034, "duration_str": "340μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 84.78, "width_percent": 0.297}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9452488, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.077, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9456942, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.304, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.946145, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.54, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9466038, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 85.775, "width_percent": 0.227}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.947047, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.002, "width_percent": 0.244}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.947533, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.247, "width_percent": 0.227}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.948367, "duration": 0.00045, "duration_str": "450μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.474, "width_percent": 0.393}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.949122, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 86.867, "width_percent": 0.262}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.94964, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.129, "width_percent": 0.244}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.950238, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.373, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_id` = ? order by `order` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.951498, "duration": 0.00054, "duration_str": "540μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 87.592, "width_percent": 0.472}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9523191, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.063, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9527912, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.308, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.953244, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.543, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.953708, "duration": 0.00037, "duration_str": "370μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 88.771, "width_percent": 0.323}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.954309, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.094, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9548101, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.338, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9552708, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.574, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.955718, "duration": 0.00028000000000000003, "duration_str": "280μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 89.801, "width_percent": 0.244}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.956178, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.045, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.956621, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.272, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957061, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.499, "width_percent": 0.236}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.957516, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.735, "width_percent": 0.218}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9579449, "duration": 0.00035999999999999997, "duration_str": "360μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 90.954, "width_percent": 0.314}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9584858, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.268, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9589298, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.495, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959369, "duration": 0.00026000000000000003, "duration_str": "260μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.722, "width_percent": 0.227}, {"sql": "select * from `menu_items` where `menu_items`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.959808, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 91.949, "width_percent": 0.236}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9602652, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.185, "width_percent": 0.236}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9607322, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.421, "width_percent": 0.236}, {"sql": "select `id`, `title` from `static_pages` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.961574, "duration": 0.00032, "duration_str": "320μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.656, "width_percent": 0.279}, {"sql": "select `id`, `title` from `posts` where `status` = ? order by `title` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.9622002, "duration": 0.0003, "duration_str": "300μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 92.936, "width_percent": 0.262}, {"sql": "select `id`, `name` from `categories` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.962712, "duration": 0.00027, "duration_str": "270μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.198, "width_percent": 0.236}, {"sql": "select `id`, `name`, `slug` from `brands` order by `name` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.963302, "duration": 0.00025, "duration_str": "250μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.433, "width_percent": 0.218}, {"sql": "select * from `posts` where `posts`.`id` = ? limit 1", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.0678341, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 93.652, "width_percent": 0.594}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = ? and `model_has_roles`.`model_type` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.136275, "duration": 0.00262, "duration_str": "2.62ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 94.246, "width_percent": 2.288}, {"sql": "select count(*) as aggregate from `contact_forms` where `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.141587, "duration": 0.00331, "duration_str": "3.31ms", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 96.533, "width_percent": 2.89}, {"sql": "select count(*) as aggregate from `contact_forms` where `status` = ?", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [], "start": **********.183818, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "", "source": false, "xdebug_link": null, "connection": "auvista", "explain": null, "start_percent": 99.424, "width_percent": 0.576}]}, "models": {"data": {"App\\Models\\MenuItem": {"value": 238, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Category": {"value": 210, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FCategory.php&line=1", "ajax": false, "filename": "Category.php", "line": "?"}}, "App\\Models\\StaticPage": {"value": 196, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FStaticPage.php&line=1", "ajax": false, "filename": "StaticPage.php", "line": "?"}}, "App\\Models\\Brand": {"value": 126, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Post": {"value": 113, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FPost.php&line=1", "ajax": false, "filename": "Post.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 50, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "Spatie\\Permission\\Models\\Permission": {"value": 40, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FPermission.php&line=1", "ajax": false, "filename": "Permission.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Menu": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}}, "count": 975, "is_counter": true}, "livewire": {"data": {"app.filament.resources.menu-item-resource.pages.manage-menu-items #pDNhDHybg7RzIUIYLXHT": "array:4 [\n  \"data\" => array:51 [\n    \"isMenuReordering\" => false\n    \"newMenuItemData\" => null\n    \"selectedTab\" => \"custom\"\n    \"customUrl\" => null\n    \"customTitle\" => null\n    \"selectedPages\" => []\n    \"selectedPosts\" => []\n    \"selectedCategories\" => []\n    \"selectedBrands\" => []\n    \"selectedMenuItems\" => []\n    \"editingMenuItem\" => []\n    \"refreshCounter\" => 0\n    \"isTableReordering\" => false\n    \"tableFilters\" => array:2 [\n      \"type\" => array:1 [\n        \"value\" => null\n      ]\n      \"status\" => array:1 [\n        \"value\" => null\n      ]\n    ]\n    \"tableGrouping\" => null\n    \"tableGroupingDirection\" => null\n    \"tableSearch\" => \"\"\n    \"tableSortColumn\" => null\n    \"tableSortDirection\" => null\n    \"activeTab\" => null\n    \"mountedActions\" => []\n    \"mountedActionsArguments\" => []\n    \"mountedActionsData\" => []\n    \"defaultAction\" => null\n    \"defaultActionArguments\" => null\n    \"componentFileAttachments\" => []\n    \"areFormStateUpdateHooksDisabledForTesting\" => false\n    \"mountedFormComponentActions\" => []\n    \"mountedFormComponentActionsArguments\" => []\n    \"mountedFormComponentActionsData\" => []\n    \"mountedFormComponentActionsComponents\" => []\n    \"mountedInfolistActions\" => []\n    \"mountedInfolistActionsData\" => []\n    \"mountedInfolistActionsComponent\" => null\n    \"mountedInfolistActionsInfolist\" => null\n    \"isTableLoaded\" => false\n    \"tableRecordsPerPage\" => 10\n    \"tableColumnSearches\" => []\n    \"toggledTableColumns\" => array:3 [\n      \"url\" => true\n      \"type\" => true\n      \"status\" => true\n    ]\n    \"mountedTableActions\" => []\n    \"mountedTableActionsData\" => []\n    \"mountedTableActionsArguments\" => []\n    \"mountedTableActionRecord\" => null\n    \"defaultTableAction\" => null\n    \"defaultTableActionArguments\" => null\n    \"defaultTableActionRecord\" => null\n    \"selectedTableRecords\" => []\n    \"mountedTableBulkAction\" => null\n    \"mountedTableBulkActionData\" => []\n    \"tableDeferredFilters\" => null\n    \"paginators\" => []\n  ]\n  \"name\" => \"app.filament.resources.menu-item-resource.pages.manage-menu-items\"\n  \"component\" => \"App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems\"\n  \"id\" => \"pDNhDHybg7RzIUIYLXHT\"\n]", "filament.livewire.global-search #Y1Mh0Lhjd8UwKMBf0Mm5": "array:4 [\n  \"data\" => array:1 [\n    \"search\" => \"\"\n  ]\n  \"name\" => \"filament.livewire.global-search\"\n  \"component\" => \"Filament\\Livewire\\GlobalSearch\"\n  \"id\" => \"Y1Mh0Lhjd8UwKMBf0Mm5\"\n]", "language-switcher #rhmj892R6GmFJKv2UTC3": "array:4 [\n  \"data\" => []\n  \"name\" => \"language-switcher\"\n  \"component\" => \"App\\Livewire\\LanguageSwitcher\"\n  \"id\" => \"rhmj892R6GmFJKv2UTC3\"\n]", "filament.livewire.notifications #HBApkKX7eIoQmR0ozqoD": "array:4 [\n  \"data\" => array:2 [\n    \"isFilamentNotificationsComponent\" => true\n    \"notifications\" => Filament\\Notifications\\Collection {#2259\n      #items: []\n      #escapeWhenCastingToString: false\n    }\n  ]\n  \"name\" => \"filament.livewire.notifications\"\n  \"component\" => \"Filament\\Livewire\\Notifications\"\n  \"id\" => \"HBApkKX7eIoQmR0ozqoD\"\n]"}, "count": 4}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/admin/menu-items/5", "action_name": "filament.admin.resources.menu-items.index", "controller_action": "App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems", "uri": "GET admin/menu-items/{menu}", "controller": "App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems@render<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "admin/menu-items", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Ffilament%2Ffilament%2Fsrc%2FPages%2FBasePage.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">vendor/filament/filament/src/Pages/BasePage.php:51-59</a>", "middleware": "panel:admin, Illuminate\\Cookie\\Middleware\\EncryptCookies, Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse, Illuminate\\Session\\Middleware\\StartSession, Filament\\Http\\Middleware\\AuthenticateSession, Illuminate\\View\\Middleware\\ShareErrorsFromSession, Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken, Illuminate\\Routing\\Middleware\\SubstituteBindings, Filament\\Http\\Middleware\\DisableBladeIconComponents, Filament\\Http\\Middleware\\DispatchServingFilamentEvent, App\\Http\\Middleware\\LogLogoutMiddleware, App\\Http\\Middleware\\LocaleMiddleware, Filament\\Http\\Middleware\\Authenticate", "duration": "7s", "peak_memory": "56MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Android&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"131 characters\">Mozilla/5.0 (Linux; Android 6.0; Nexus 5 Build/MRA58N) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Mobile Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://auvista.test/admin/menus</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IlZHcEgyVlVveVBFTUt4SFhFdURTUGc9PSIsInZhbHVlIjoiUmRxVS85ME8rUnA2M0NVRStPZGVjSG1HMk1BOTJRQkFuMXZrN1BPaGh3T0JJdTVwaXVMVXF0SlNlTk1lVXo1c0FVQ1d0TVRPSU5zVml0MnRhSmdyVW1jU215ZXMvd3lmcVBxUmF6cElBc1dkMmNPQllmYjlJTFkyUkZiZUZjN04iLCJtYWMiOiI2ODNlMGIzNjljYzlmMjc3MzE4ZGYzZmZhOTAwMzdhMTgwODg1OTcyMmZmMTQ3MDBkNGU5YzhmNWJjNjBmNDZiIiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkRXOGFucVo2SzZvdUhLS3JqNzdLeXc9PSIsInZhbHVlIjoiUmVKOHkrSjVTa1dvQVg3bU5WZHJBV3pnckYxOVpXbzdRbmRjMjVJdGhkNStnU3lVYzN4dDlPYmZaU0p4bGR1QzM1UE0yN2kxam9ReDlVdHBkUFlHcDN5RTRHWXlKRG9GVjhDdTN2Vm1pWUtjN2VsNngvLzJ4T1h3MUlpU2V2d0UiLCJtYWMiOiIwZGU3ZWE5YjQwYmNjMjcyNGRjY2UzNzBlNGEzMTBiNzU4YWIwOTU3Zjk1Mzc5ODNhN2M3NzdmZGVhZGZlYzZiIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-12690620 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">ALiu3MXg2qeWtslysnYzKUjGVbJ1TKvwt3KhqDy1</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12690620\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1841800737 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 05:07:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6InlEYTM3N2dHYURKVmM5N1ozY1ZJWXc9PSIsInZhbHVlIjoiY09KajNleFN4ajJveTFwN1BYR1RkaUVDYmxjdEV0THJodVNqbmlsNUJkZjc4N2g3b2gra2kzRDhMZHAwYk40cWJqV2xONFVpMkFBNmExNm9wbVpsQlQyODRXcWp3RmVvVG12VUtYQW90RXdhWHMrSGZYR3cxaGJXN1FCTFZkeW8iLCJtYWMiOiI5NWRmYjBmY2VjZjNlNTRjZTZhMmM0ODkzODk0M2JiN2Y0ZDFhNThmNmQyN2Y3NGYwMThiMmMxOGI1MGZkN2RiIiwidGFnIjoiIn0%3D; expires=Thu, 17 Jul 2025 07:07:26 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"443 characters\">laravel_session=eyJpdiI6InhtSWtyak1wVzJFMTZ0TUlXb3B3enc9PSIsInZhbHVlIjoiUFQ0TkNPZVRLUlMvMFJpY0d1bDJLS3haeElLbUM5NmlZZ1BHM2o2ZWp6cUJyU3hHV21DOHhpNUZUSm9Vc1VRWUZaL1RsWmJUQjhrbHBBQ3RvS0N6RkM2cXh6WnMzWjZUR2F5NG03M0pCaUp4UUs1THVjOW9PdHROdlVDcWJhZWYiLCJtYWMiOiI1ZTVjM2UxZDUxMDBiN2IzNjNmYjkxYzIyMjhlN2I4ODVkNjdkNzc2OTMxYzQ4NTg2OWNjNmVmMzZmYzJhZjljIiwidGFnIjoiIn0%3D; expires=Thu, 17 Jul 2025 07:07:26 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6InlEYTM3N2dHYURKVmM5N1ozY1ZJWXc9PSIsInZhbHVlIjoiY09KajNleFN4ajJveTFwN1BYR1RkaUVDYmxjdEV0THJodVNqbmlsNUJkZjc4N2g3b2gra2kzRDhMZHAwYk40cWJqV2xONFVpMkFBNmExNm9wbVpsQlQyODRXcWp3RmVvVG12VUtYQW90RXdhWHMrSGZYR3cxaGJXN1FCTFZkeW8iLCJtYWMiOiI5NWRmYjBmY2VjZjNlNTRjZTZhMmM0ODkzODk0M2JiN2Y0ZDFhNThmNmQyN2Y3NGYwMThiMmMxOGI1MGZkN2RiIiwidGFnIjoiIn0%3D; expires=Thu, 17-Jul-2025 07:07:26 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"415 characters\">laravel_session=eyJpdiI6InhtSWtyak1wVzJFMTZ0TUlXb3B3enc9PSIsInZhbHVlIjoiUFQ0TkNPZVRLUlMvMFJpY0d1bDJLS3haeElLbUM5NmlZZ1BHM2o2ZWp6cUJyU3hHV21DOHhpNUZUSm9Vc1VRWUZaL1RsWmJUQjhrbHBBQ3RvS0N6RkM2cXh6WnMzWjZUR2F5NG03M0pCaUp4UUs1THVjOW9PdHROdlVDcWJhZWYiLCJtYWMiOiI1ZTVjM2UxZDUxMDBiN2IzNjNmYjkxYzIyMjhlN2I4ODVkNjdkNzc2OTMxYzQ4NTg2OWNjNmVmMzZmYzJhZjljIiwidGFnIjoiIn0%3D; expires=Thu, 17-Jul-2025 07:07:26 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1841800737\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-597214895 data-indent-pad=\"  \"><span class=sf-dump-note>array:9</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">29xepOjKYVuCvYPZVra6ERSDFUYgTYUqgFbuqBhK</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"39 characters\">https://auvista.test/admin/menu-items/5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>url</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>password_hash_web</span>\" => \"<span class=sf-dump-str title=\"60 characters\">$2y$12$2uJGkMnwk7Lj203GgLThU.EI1Z7VNTd1oXu5zgFDqiWVi2sEnDYEy</span>\"\n  \"<span class=sf-dump-key>current_menu_id</span>\" => <span class=sf-dump-num>5</span>\n  \"<span class=sf-dump-key>filament</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-597214895\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/admin/menu-items/5", "action_name": "filament.admin.resources.menu-items.index", "controller_action": "App\\Filament\\Resources\\MenuItemResource\\Pages\\ManageMenuItems"}, "badge": null}}