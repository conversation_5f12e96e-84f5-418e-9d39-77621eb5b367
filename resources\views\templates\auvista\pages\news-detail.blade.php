@extends('templates.auvista.layouts.default')

@section('title', ($data['post']->title ?? __('messages.news_detail')) . ' - AVPlus')
@section('meta_description', $data['post']->excerpt ?? __('messages.news_description'))

@section('content')
    <main id="main" class="site-main">
        <div class="page-wrapper blog-archive mb-7 lg:mb-[54px] mt-12 lg:mt-[70px]">
            <div class="container mx-auto mb-12">
                <div class="flex flex-col lg:flex-row gap-x-8">
                    <div class="w-full">
                        <h1 class="text-primary-base font-bold text-2xl md:text-[38px]/[1.4] mb-6">
                            {{ $data['post']->title }}
                        </h1>

                        <!-- Post Meta Info -->
                        <div class="flex items-center space-x-4 text-gray-500 mb-6">
                            @if($data['primaryCategory'])
                                <span class="bg-primary-background1 text-secondary-main rounded-sm px-2 py-1">
                                    {{ $data['primaryCategory']->name }}
                                </span>
                                <span class="separator w-[1px] h-6 bg-primary-border"></span>
                            @endif
                            <div class="flex items-center space-x-1">
                                <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                    xmlns="http://www.w3.org/2000/svg">
                                    <path
                                        d="M12 4.03577C10.22 4.03577 8.47991 4.56361 6.99987 5.55254C5.51983 6.54147 4.36628 7.94708 3.68509 9.59162C3.0039 11.2362 2.82567 13.0458 3.17294 14.7916C3.5202 16.5374 4.37737 18.1411 5.63604 19.3997C6.89472 20.6584 8.49836 21.5156 10.2442 21.8628C11.99 22.2101 13.7996 22.0319 15.4442 21.3507C17.0887 20.6695 18.4943 19.5159 19.4832 18.0359C20.4722 16.5559 21 14.8158 21 13.0358C20.9973 10.6497 20.0482 8.36206 18.361 6.67482C16.6737 4.98758 14.3861 4.0385 12 4.03577ZM12 20.5358C10.5166 20.5358 9.0666 20.0959 7.83323 19.2718C6.59986 18.4477 5.63856 17.2763 5.07091 15.9059C4.50325 14.5354 4.35473 13.0274 4.64411 11.5726C4.9335 10.1177 5.64781 8.78136 6.6967 7.73247C7.7456 6.68357 9.08197 5.96927 10.5368 5.67988C11.9917 5.39049 13.4997 5.53901 14.8701 6.10667C16.2406 6.67433 17.4119 7.63562 18.236 8.86899C19.0601 10.1024 19.5 11.5524 19.5 13.0358C19.4978 15.0242 18.7069 16.9306 17.3008 18.3366C15.8948 19.7426 13.9884 20.5335 12 20.5358ZM16.2806 8.75514C16.3504 8.8248 16.4057 8.90751 16.4434 8.99856C16.4812 9.08961 16.5006 9.18721 16.5006 9.28577C16.5006 9.38433 16.4812 9.48192 16.4434 9.57297C16.4057 9.66402 16.3504 9.74674 16.2806 9.81639L12.5306 13.5664C12.4609 13.6361 12.3782 13.6913 12.2872 13.7291C12.1961 13.7668 12.0986 13.7862 12 13.7862C11.9015 13.7862 11.8039 13.7668 11.7128 13.7291C11.6218 13.6913 11.5391 13.6361 11.4694 13.5664C11.3997 13.4967 11.3444 13.414 11.3067 13.3229C11.269 13.2319 11.2496 13.1343 11.2496 13.0358C11.2496 12.9372 11.269 12.8396 11.3067 12.7486C11.3444 12.6576 11.3997 12.5748 11.4694 12.5051L15.2194 8.75514C15.289 8.68541 15.3718 8.63009 15.4628 8.59235C15.5538 8.5546 15.6514 8.53518 15.75 8.53518C15.8486 8.53518 15.9462 8.5546 16.0372 8.59235C16.1283 8.63009 16.211 8.68541 16.2806 8.75514ZM9 1.78577C9 1.58685 9.07902 1.39609 9.21967 1.25544C9.36033 1.11478 9.55109 1.03577 9.75 1.03577H14.25C14.4489 1.03577 14.6397 1.11478 14.7803 1.25544C14.921 1.39609 15 1.58685 15 1.78577C15 1.98468 14.921 2.17544 14.7803 2.3161C14.6397 2.45675 14.4489 2.53577 14.25 2.53577H9.75C9.55109 2.53577 9.36033 2.45675 9.21967 2.3161C9.07902 2.17544 9 1.98468 9 1.78577Z"
                                        fill="#535563" />
                                </svg>
                                <span>{{ \Carbon\Carbon::parse($data['post']->updated_at)->diffForHumans() }}</span>
                            </div>
                            @if($data['post']->published_at)
                                <span class="separator w-[1px] h-6 bg-primary-border"></span>
                                <span>{{ $data['post']->published_at->format('d/m/Y') }}</span>
                            @endif
                        </div>

                        @if($data['post']->excerpt)
                            <p class="text-primary-gray2 mb-6">
                                {{ $data['post']->excerpt }}
                            </p>
                        @endif

                        @if(!empty($data['tocItems']))
                            <div class="toc mb-6">
                                <div class="bg-primary-border p-6 rounded-lg shadow-lg w-full font-bold space-y-5">
                                    <div class="font-bold flex justify-between items-center">
                                        <span
                                            class="inline-flex items-center gap-4 font-bold text-lg leading-[1.4] text-secondary-main"><svg
                                                width="28" height="28" viewBox="0 0 28 28" fill="none"
                                                xmlns="http://www.w3.org/2000/svg">
                                                <path d="M0 5.25H28V7H0V5.25Z" fill="#246DDA" />
                                                <path d="M0 12.8333H28V14.5833H0V12.8333Z" fill="#246DDA" />
                                                <path d="M0 20.4167H28V22.1667H0V20.4167Z" fill="#246DDA" />
                                            </svg>
                                            MỤC LỤC</span>
                                        <span class="toggle-toc cursor-pointer" id="toc-toggle"><svg width="20" height="20"
                                                viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg"
                                                class="transition-transform duration-300 ease-in-out">
                                                <path
                                                    d="M0.837903 5.01354C1.10812 4.73809 1.47456 4.58334 1.85665 4.58334C2.23873 4.58334 2.60518 4.73809 2.87539 5.01354L10.008 12.2867L17.1407 5.01354C17.4125 4.74589 17.7765 4.59779 18.1543 4.60114C18.5321 4.60449 18.8935 4.75902 19.1606 5.03144C19.4278 5.30386 19.5793 5.67239 19.5826 6.05764C19.5859 6.4429 19.4407 6.81405 19.1782 7.09117L11.0268 15.4031C10.7566 15.6786 10.3901 15.8333 10.008 15.8333C9.62596 15.8333 9.25952 15.6786 8.9893 15.4031L0.837903 7.09117C0.567768 6.81563 0.416015 6.44197 0.416015 6.05236C0.416015 5.66274 0.567768 5.28908 0.837903 5.01354Z"
                                                    fill="#246DDA" />
                                            </svg>
                                        </span>
                                    </div>
                                    <div id="toc-content" class="transition-all duration-300 ease-in-out">
                                        {!! renderTocHtml($data['tocItems']) !!}
                                    </div>

                                    <p class="transition-all duration-300 ease-in-out" id="toc-note">
                                        Lưu ý: Nhấp vào các mục để di chuyển đến phần tương ứng trong bài viết.
                                    </p>
                                </div>
                            </div>
                        @endif

                        <!-- Featured Image -->
                        <div class="mb-6">
                            <img src="{{ $data['post']->image ? getImageUrl($data['post']->image, 'post', 800, 450) : asset('storage/images/img-big-post.png') }}"
                                alt="{{ $data['post']->title }}" class="rounded-lg w-full" />
                        </div>

                        <!-- Main Content -->

                        <div class="post-content prose max-w-none">
                            {!! $data['post']->content !!}
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="w-full lg:max-w-[25.5319149%] relative">
                        <div class="sticky top-4">
                            <h4 class="text-2xl font-bold mb-6">{{ __('messages.featured_news') }}</h4>
                            <div class="space-y-5">
                                @foreach($data['featuredPosts'] as $featuredPost)
                                    <div class="flex items-center">
                                        <a href="{{ route('news.detail', $featuredPost->slug) }}"
                                            class="w-full max-w-[47.1111111%] aspect-[1.767] overflow-hidden rounded-lg mr-4 block"
                                            style="min-width: 50%;">
                                            <img src="{{ $featuredPost->image ? getImageUrl($featuredPost->image, 'post', 600, 400) : asset('storage/images/img-post-2.jpg') }}"
                                                alt="{{ $featuredPost->title }}"
                                                class="w-full h-full object-cover hover:scale-105 transition-transform duration-300" />
                                        </a>
                                        <div style="min-width: 50%;">
                                            <div class="flex items-center justify-between gap-2">
                                                <span
                                                    class="inline-block bg-primary-background1 px-2 py-1 rounded-sm text-secondary-main whitespace-nowrap xl:text-sm" style="min-width: 69px;">
                                                    <!--  cắt ngắn néu dài quá 10 kí tự -->
                                                    {{ Str::limit($featuredPost->categories->first() ? $featuredPost->categories->first()->name : __('messages.news'), 7, '...') }}
                                                </span>
                                                <span class="separator w-[1px] h-6 bg-primary-border"></span>
                                                <span
                                                    class="text-primary-gray2 flex gap-1 items-center justify-start xl:text-sm whitespace-nowrap">
                                                    <svg width="24" height="25" viewBox="0 0 24 25" fill="none"
                                                        xmlns="http://www.w3.org/2000/svg">
                                                        <path
                                                            d="M12 4.03577C10.22 4.03577 8.47991 4.56361 6.99987 5.55254C5.51983 6.54147 4.36628 7.94708 3.68509 9.59162C3.0039 11.2362 2.82567 13.0458 3.17294 14.7916C3.5202 16.5374 4.37737 18.1411 5.63604 19.3997C6.89472 20.6584 8.49836 21.5156 10.2442 21.8628C11.99 22.2101 13.7996 22.0319 15.4442 21.3507C17.0887 20.6695 18.4943 19.5159 19.4832 18.0359C20.4722 16.5559 21 14.8158 21 13.0358C20.9973 10.6497 20.0482 8.36206 18.361 6.67482C16.6737 4.98758 14.3861 4.0385 12 4.03577ZM12 20.5358C10.5166 20.5358 9.0666 20.0959 7.83323 19.2718C6.59986 18.4477 5.63856 17.2763 5.07091 15.9059C4.50325 14.5354 4.35473 13.0274 4.64411 11.5726C4.9335 10.1177 5.64781 8.78136 6.6967 7.73247C7.7456 6.68357 9.08197 5.96927 10.5368 5.67988C11.9917 5.39049 13.4997 5.53901 14.8701 6.10667C16.2406 6.67433 17.4119 7.63562 18.236 8.86899C19.0601 10.1024 19.5 11.5524 19.5 13.0358C19.4978 15.0242 18.7069 16.9306 17.3008 18.3366C15.8948 19.7426 13.9884 20.5335 12 20.5358ZM16.2806 8.75514C16.3504 8.8248 16.4057 8.90751 16.4434 8.99856C16.4812 9.08961 16.5006 9.18721 16.5006 9.28577C16.5006 9.38433 16.4812 9.48192 16.4434 9.57297C16.4057 9.66402 16.3504 9.74674 16.2806 9.81639L12.5306 13.5664C12.4609 13.6361 12.3782 13.6913 12.2872 13.7291C12.1961 13.7668 12.0986 13.7862 12 13.7862C11.9015 13.7862 11.8039 13.7668 11.7128 13.7291C11.6218 13.6913 11.5391 13.6361 11.4694 13.5664C11.3997 13.4967 11.3444 13.414 11.3067 13.3229C11.269 13.2319 11.2496 13.1343 11.2496 13.0358C11.2496 12.9372 11.269 12.8396 11.3067 12.7486C11.3444 12.6576 11.3997 12.5748 11.4694 12.5051L15.2194 8.75514C15.289 8.68541 15.3718 8.63009 15.4628 8.59235C15.5538 8.5546 15.6514 8.53518 15.75 8.53518C15.8486 8.53518 15.9462 8.5546 16.0372 8.59235C16.1283 8.63009 16.211 8.68541 16.2806 8.75514ZM9 1.78577C9 1.58685 9.07902 1.39609 9.21967 1.25544C9.36033 1.11478 9.55109 1.03577 9.75 1.03577H14.25C14.4489 1.03577 14.6397 1.11478 14.7803 1.25544C14.921 1.39609 15 1.58685 15 1.78577C15 1.98468 14.921 2.17544 14.7803 2.3161C14.6397 2.45675 14.4489 2.53577 14.25 2.53577H9.75C9.55109 2.53577 9.36033 2.45675 9.21967 2.3161C9.07902 2.17544 9 1.98468 9 1.78577Z"
                                                            fill="#535563" />
                                                    </svg>
                                                    <!-- Thời gian cập nhật dạng date ago -->

                                                    {{ \Carbon\Carbon::parse($featuredPost->updated_at)->diffForHumans() }}
                                                </span>
                                            </div>
                                            <h4 class="mt-2">
                                                <a href="{{ route('news.detail', $featuredPost->slug) }}"
                                                    class="line-clamp-2 text-primary-base font-medium hover:text-primary-main">
                                                    {{ $featuredPost->title }}
                                                </a>
                                            </h4>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Divider -->
            <div class="container mx-auto mb-12 md:mb-16">
                <span class="block w-full h-[1px] bg-primary-border"></span>
            </div>

            <!-- Related Posts -->
            @if($data['relatedPosts']->count() > 0)
                <div class="container mx-auto related-slider">
                    <h2 class="text-primary-base font-bold text-2xl md:text-[38px]/[1.4] mb-[17px]">
                        {{ __('messages.related_news') }}
                    </h2>
                    <div class="swiper-slider relative -ml-[15px] -mr-[15px]" data-items="1.3" data-mobile="2" data-tablet="3"
                        data-desktop="3" data-large="3" data-xlarge="3" data-spacing="0" data-loop="false"
                        data-navigation="true">
                        <div class="swiper">
                            <div class="swiper-wrapper">
                                @foreach($data['relatedPosts'] as $relatedPost)
                                    @php
                                        $category = $relatedPost->categories->first();
                                        $categoryColors = [
                                            'tin-tuc-cong-nghe' => 'bg-primary-background1 text-secondary-main',
                                            'review-san-pham' => 'bg-primary-background2 text-primary-price',
                                            'huong-dan-su-dung' => 'bg-primary-background3 text-white',
                                            'kien-thuc-am-thanh' => 'bg-primary-background1 text-secondary-main',
                                            'su-kien-trien-lam' => 'bg-primary-background2 text-primary-price'
                                        ];
                                        $colorClass = $category ? ($categoryColors[$category->slug] ?? 'bg-primary-background2 text-primary-price') : 'bg-primary-background2 text-primary-price';
                                    @endphp
                                    <div class="swiper-slide p-[15px] h-auto">
                                        <div class="bg-white rounded-lg shadow-new-shadow overflow-hidden flex flex-col h-full">
                                            <a href="{{ route('news.detail', $relatedPost->slug) }}"
                                                class="block overflow-hidden aspect-[1.77777778] flex-shrink-0">
                                                @php
                                                    $relatedPlaceholders = ['img-post-2.jpg', 'img-post-3.jpg', 'img-post-4.jpg', 'img-post-5.jpg'];
                                                    $randomRelatedPlaceholder = $relatedPlaceholders[array_rand($relatedPlaceholders)];
                                                @endphp
                                                <img src="{{ $relatedPost->image ? getImageUrl($relatedPost->image, 'post', 400, 225) : asset('storage/images/' . $randomRelatedPlaceholder) }}"
                                                    alt="{{ $relatedPost->title }}"
                                                    class="w-full h-full object-cover hover:scale-110 transition-transform duration-300" />
                                            </a>
                                            <div class="p-6 flex flex-col flex-grow">
                                                <div class="flex items-center gap-2 mb-3 flex-shrink-0">
                                                    @if($category)
                                                        <span
                                                            class="inline-block {{ $colorClass }} px-2 py-1 rounded-sm text-sm">{{ $category->name }}</span>
                                                        <span class="separator w-[1px] h-4 bg-primary-border"></span>
                                                    @endif
                                                    <span class="text-primary-gray2 flex gap-1 items-center text-sm">
                                                        <svg width="16" height="16" viewBox="0 0 24 25" fill="none"
                                                            xmlns="http://www.w3.org/2000/svg">
                                                            <path
                                                                d="M12 4.03577C10.22 4.03577 8.47991 4.56361 6.99987 5.55254C5.51983 6.54147 4.36628 7.94708 3.68509 9.59162C3.0039 11.2362 2.82567 13.0458 3.17294 14.7916C3.5202 16.5374 4.37737 18.1411 5.63604 19.3997C6.89472 20.6584 8.49836 21.5156 10.2442 21.8628C11.99 22.2101 13.7996 22.0319 15.4442 21.3507C17.0887 20.6695 18.4943 19.5159 19.4832 18.0359C20.4722 16.5559 21 14.8158 21 13.0358C20.9973 10.6497 20.0482 8.36206 18.361 6.67482C16.6737 4.98758 14.3861 4.0385 12 4.03577ZM12 20.5358C10.5166 20.5358 9.0666 20.0959 7.83323 19.2718C6.59986 18.4477 5.63856 17.2763 5.07091 15.9059C4.50325 14.5354 4.35473 13.0274 4.64411 11.5726C4.9335 10.1177 5.64781 8.78136 6.6967 7.73247C7.7456 6.68357 9.08197 5.96927 10.5368 5.67988C11.9917 5.39049 13.4997 5.53901 14.8701 6.10667C16.2406 6.67433 17.4119 7.63562 18.236 8.86899C19.0601 10.1024 19.5 11.5524 19.5 13.0358C19.4978 15.0242 18.7069 16.9306 17.3008 18.3366C15.8948 19.7426 13.9884 20.5335 12 20.5358ZM16.2806 8.75514C16.3504 8.8248 16.4057 8.90751 16.4434 8.99856C16.4812 9.08961 16.5006 9.18721 16.5006 9.28577C16.5006 9.38433 16.4812 9.48192 16.4434 9.57297C16.4057 9.66402 16.3504 9.74674 16.2806 9.81639L12.5306 13.5664C12.4609 13.6361 12.3782 13.6913 12.2872 13.7291C12.1961 13.7668 12.0986 13.7862 12 13.7862C11.9015 13.7862 11.8039 13.7668 11.7128 13.7291C11.6218 13.6913 11.5391 13.6361 11.4694 13.5664C11.3997 13.4967 11.3444 13.414 11.3067 13.3229C11.269 13.2319 11.2496 13.1343 11.2496 13.0358C11.2496 12.9372 11.269 12.8396 11.3067 12.7486C11.3444 12.6576 11.3997 12.5748 11.4694 12.5051L15.2194 8.75514C15.289 8.68541 15.3718 8.63009 15.4628 8.59235C15.5538 8.5546 15.6514 8.53518 15.75 8.53518C15.8486 8.53518 15.9462 8.5546 16.0372 8.59235C16.1283 8.63009 16.211 8.68541 16.2806 8.75514ZM9 1.78577C9 1.58685 9.07902 1.39609 9.21967 1.25544C9.36033 1.11478 9.55109 1.03577 9.75 1.03577H14.25C14.4489 1.03577 14.6397 1.11478 14.7803 1.25544C14.921 1.39609 15 1.58685 15 1.78577C15 1.98468 14.921 2.17544 14.7803 2.3161C14.6397 2.45675 14.4489 2.53577 14.25 2.53577H9.75C9.55109 2.53577 9.36033 2.45675 9.21967 2.3161C9.07902 2.17544 9 1.98468 9 1.78577Z"
                                                                fill="#535563" />
                                                        </svg>
                                                        {{ rand(4, 8) }} min read
                                                    </span>
                                                </div>
                                                <h3 class="text-lg font-semibold mb-3 flex-shrink-0">
                                                    <a href="{{ route('news.detail', $relatedPost->slug) }}"
                                                        class="text-primary-base hover:text-primary-main line-clamp-2">
                                                        {{ $relatedPost->title }}
                                                    </a>
                                                </h3>
                                                @if($relatedPost->excerpt)
                                                    <div class="flex-grow">
                                                        <p class="text-primary-gray2 text-sm line-clamp-3">
                                                            {{ $relatedPost->excerpt }}
                                                        </p>
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            <!-- Navigation buttons -->
                            <div class="swiper-button-next"></div>
                            <div class="swiper-button-prev"></div>
                        </div>
                    </div>
                </div>
            @else
                <!-- Fallback khi không có bài viết liên quan nào -->
                <div class="container mx-auto">
                    <h2 class="text-primary-base font-bold text-2xl md:text-[38px]/[1.4] mb-[17px]">
                        {{ __('messages.related_news') }}
                    </h2>
                    <div class="text-center py-12">
                        <p class="text-gray-500 text-lg">{{ __('messages.related_message') }}</p>
                    </div>
                </div>
            @endif

            <!-- Spacing before footer -->
            <div class="mb-12 lg:mb-[70px]"></div>
        </div>
    </main>
@endsection

@push('scripts')
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            // TOC Toggle functionality
            const tocToggle = document.getElementById('toc-toggle');
            const tocContent = document.getElementById('toc-content');
            const tocNote = document.getElementById('toc-note');
            const toggleIcon = tocToggle?.querySelector('svg');

            let isExpanded = true; // TOC is expanded by default

            if (tocToggle) {
                tocToggle.addEventListener('click', function () {
                    if (isExpanded) {
                        // Collapse TOC
                        tocContent.style.maxHeight = '0';
                        tocContent.style.opacity = '0';
                        tocContent.style.overflow = 'hidden';
                        tocNote.style.maxHeight = '0';
                        tocNote.style.opacity = '0';
                        tocNote.style.overflow = 'hidden';

                        // Rotate arrow up
                        toggleIcon.style.transform = 'rotate(180deg)';

                        isExpanded = false;
                    } else {
                        // Expand TOC
                        tocContent.style.maxHeight = 'none';
                        tocContent.style.opacity = '1';
                        tocContent.style.overflow = 'visible';
                        tocNote.style.maxHeight = 'none';
                        tocNote.style.opacity = '1';
                        tocNote.style.overflow = 'visible';

                        // Rotate arrow down
                        toggleIcon.style.transform = 'rotate(0deg)';

                        isExpanded = true;
                    }
                });
            }

            // Smooth scrolling for TOC links
            const tocLinks = document.querySelectorAll('#toc-content a[href^="#"]');
            tocLinks.forEach(link => {
                link.addEventListener('click', function (e) {
                    e.preventDefault();
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);

                    if (targetElement) {
                        const offsetTop = targetElement.offsetTop - 100; // Offset for fixed header
                        window.scrollTo({
                            top: offsetTop,
                            behavior: 'smooth'
                        });
                    }
                });
            });
        });
    </script>
@endpush