<?php

namespace App\Livewire;

use Livewire\Component;
use App\Models\Product;
use Illuminate\Support\Facades\Session;

class AddToCartButton extends Component
{
    public $productId;
    public $quantity = 1;
    public $showQuantity = true;
    public $cartItems = [];
    public $productStock = 99; // Default stock
    public $simpleMode = false; // Add this property

    public function mount($productId, $productStock = 99, $showQuantity = true, $simpleMode = false)
    {
        \Log::info('AddToCartButton mounted', [
            'productId' => $productId,
            'productStock' => $productStock,
            'showQuantity' => $showQuantity,
            'simpleMode' => $simpleMode
        ]);

        $this->productId = $productId;
        $this->productStock = $productStock;
        $this->showQuantity = $showQuantity;
        $this->simpleMode = $simpleMode;
        $this->cartItems = Session::get('cart', []);
    }

    public function increment()
    {
        if ($this->quantity < $this->productStock) {
            $this->quantity++;
        }
    }

    public function decrement()
    {
        if ($this->quantity > 1) {
            $this->quantity--;
        }
    }

    public function addToCart()
    {
        try {
            $product = Product::findOrFail($this->productId);
            
            // Lấy giỏ hàng hiện tại từ Session
            $cartItems = Session::get('cart', []);
            
            // Thêm vào giỏ hàng
            if (isset($cartItems[$this->productId])) {
                $cartItems[$this->productId] += $this->quantity;
            } else {
                $cartItems[$this->productId] = $this->quantity;
            }
            
            // Lưu lại vào Session và force save
            Session::put('cart', $cartItems);
            Session::save(); // Force save session
            
            // Debug session
            \Illuminate\Support\Facades\Log::info('Added to cart via Livewire:', [
                'productId' => $this->productId,
                'quantity' => $this->quantity,
                'cart' => $cartItems,
                'session_id' => Session::getId(),
                'cart_count' => count($cartItems)
            ]);
            
            // Cập nhật cartItems trong component
            $this->cartItems = $cartItems;
            
            // Emit sự kiện cập nhật giỏ hàng ngay lập tức
            $this->dispatch('show-notification', [
                'type' => 'success',
                'message' => 'Sản phẩm đã được thêm vào giỏ hàng.'
            ]);
            
            // Force refresh all cart components immediately
            $this->dispatch('cart-updated');
            $this->dispatch('cart-updated-popup');
            $this->dispatch('cart:refresh');
            
            // Also dispatch to specific components
            $this->dispatch('cart-updated')->to(\App\Livewire\CartPage::class);
            $this->dispatch('cart-updated')->to(\App\Livewire\CartCount::class);
            $this->dispatch('cart-updated-popup')->to(\App\Livewire\CartPopup::class);
            
            // Emit sự kiện thành công
            $this->dispatch('success', [
                'message' => 'Sản phẩm đã được thêm vào giỏ hàng'
            ]);
            
            return response()->json([
                'success' => true,
                'message' => 'Sản phẩm đã được thêm vào giỏ hàng',
                'cartCount' => count($cartItems)
            ]);
        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Error adding to cart:', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'session_path' => storage_path('framework/sessions'),
                'session_files' => glob(storage_path('framework/sessions/*'))
            ]);

            $this->dispatch('show-notification', [
                'type' => 'error',
                'message' => 'Không thể thêm sản phẩm vào giỏ hàng: ' . $e->getMessage()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Không thể thêm sản phẩm vào giỏ hàng: ' . $e->getMessage()
            ], 500);
        }
    }

    public function buyNow()
    {
        $this->addToCart();
        return redirect()->route('cart');
    }

    public function render()
    {
        if ($this->simpleMode) {
            return view('livewire.add-to-cart-button-simple');
        }

        return view('livewire.add-to-cart-button');
    }
} 