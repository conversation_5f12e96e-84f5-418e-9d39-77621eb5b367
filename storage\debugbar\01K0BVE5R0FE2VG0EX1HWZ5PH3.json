{"__meta": {"id": "01K0BVE5R0FE2VG0EX1HWZ5PH3", "datetime": "2025-07-17 16:28:51", "utime": **********.712993, "method": "GET", "uri": "/thuong-hieu", "ip": "127.0.0.1"}, "messages": {"count": 1, "messages": [{"message": "[16:28:51] LOG.info: CartPopup loadCart called: {\n    \"cartItems\": {\n        \"12\": 1\n    },\n    \"session_cart\": {\n        \"12\": 1\n    },\n    \"total\": 350000000,\n    \"session_id\": \"y6qoLsHnV2DFsQpcZMnA28moh7ZYgBrNAG7wtpmZ\"\n}", "message_html": null, "is_string": false, "label": "info", "time": **********.672062, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1752744530.739032, "end": **********.713034, "duration": 0.9740018844604492, "duration_str": "974ms", "measures": [{"label": "Booting", "start": 1752744530.739032, "relative_start": 0, "end": **********.34542, "relative_end": **********.34542, "duration": 0.****************, "duration_str": "606ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.345435, "relative_start": 0.****************, "end": **********.713036, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "368ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.509785, "relative_start": 0.****************, "end": **********.514206, "relative_end": **********.514206, "duration": 0.0044209957122802734, "duration_str": "4.42ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.561705, "relative_start": 0.****************, "end": **********.70971, "relative_end": **********.70971, "duration": 0.*****************, "duration_str": "148ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: templates.auvista.pages.brands", "start": **********.56498, "relative_start": 0.****************, "end": **********.56498, "relative_end": **********.56498, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: pagination::tailwind", "start": **********.612262, "relative_start": 0.**************, "end": **********.612262, "relative_end": **********.612262, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.layouts.default", "start": **********.612985, "relative_start": 0.8739528656005859, "end": **********.612985, "relative_end": **********.612985, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.components.product_styles", "start": **********.622573, "relative_start": 0.8835408687591553, "end": **********.622573, "relative_end": **********.622573, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.header", "start": **********.623213, "relative_start": 0.884181022644043, "end": **********.623213, "relative_end": **********.623213, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.cart-count", "start": **********.635899, "relative_start": 0.896867036819458, "end": **********.635899, "relative_end": **********.635899, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: livewire.cart-count", "start": **********.657934, "relative_start": 0.9189019203186035, "end": **********.657934, "relative_end": **********.657934, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.breadcrumb", "start": **********.659412, "relative_start": 0.9203798770904541, "end": **********.659412, "relative_end": **********.659412, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: H:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "start": **********.672633, "relative_start": 0.933600902557373, "end": **********.672633, "relative_end": **********.672633, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: templates.auvista.blocks.footer", "start": **********.682258, "relative_start": 0.9432258605957031, "end": **********.682258, "relative_end": **********.682258, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 43664784, "peak_usage_str": "42MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "11.x", "tooltip": {"Laravel Version": "11.45.1", "PHP Version": "8.3.16", "Environment": "local", "Debug Mode": "Enabled", "URL": "auvista.test", "Timezone": "Asia/Ho_Chi_Minh", "Locale": "vi"}}, "views": {"count": 10, "nb_templates": 10, "templates": [{"name": "templates.auvista.pages.brands", "param_count": null, "params": [], "start": **********.564962, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/pages/brands.blade.phptemplates.auvista.pages.brands", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fpages%2Fbrands.blade.php&line=1", "ajax": false, "filename": "brands.blade.php", "line": "?"}}, {"name": "pagination::tailwind", "param_count": null, "params": [], "start": **********.612244, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/vendor/pagination/tailwind.blade.phppagination::tailwind", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Fvendor%2Fpagination%2Ftailwind.blade.php&line=1", "ajax": false, "filename": "tailwind.blade.php", "line": "?"}}, {"name": "templates.auvista.layouts.default", "param_count": null, "params": [], "start": **********.61297, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/layouts/default.blade.phptemplates.auvista.layouts.default", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Flayouts%2Fdefault.blade.php&line=1", "ajax": false, "filename": "default.blade.php", "line": "?"}}, {"name": "templates.auvista.components.product_styles", "param_count": null, "params": [], "start": **********.622555, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/components/product_styles.blade.phptemplates.auvista.components.product_styles", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fcomponents%2Fproduct_styles.blade.php&line=1", "ajax": false, "filename": "product_styles.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.header", "param_count": null, "params": [], "start": **********.623196, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.phptemplates.auvista.blocks.header", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Fheader.blade.php&line=1", "ajax": false, "filename": "header.blade.php", "line": "?"}}, {"name": "livewire.cart-count", "param_count": null, "params": [], "start": **********.635878, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-count.blade.phplivewire.cart-count", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-count.blade.php&line=1", "ajax": false, "filename": "cart-count.blade.php", "line": "?"}}, {"name": "livewire.cart-count", "param_count": null, "params": [], "start": **********.657918, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-count.blade.phplivewire.cart-count", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-count.blade.php&line=1", "ajax": false, "filename": "cart-count.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.breadcrumb", "param_count": null, "params": [], "start": **********.659389, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/breadcrumb.blade.phptemplates.auvista.blocks.breadcrumb", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Fbreadcrumb.blade.php&line=1", "ajax": false, "filename": "breadcrumb.blade.php", "line": "?"}}, {"name": "H:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "param_count": null, "params": [], "start": **********.672614, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.phpH:\\laragon\\www\\auvista\\resources\\views/livewire/cart-popup.blade.php", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Flivewire%2Fcart-popup.blade.php&line=1", "ajax": false, "filename": "cart-popup.blade.php", "line": "?"}}, {"name": "templates.auvista.blocks.footer", "param_count": null, "params": [], "start": **********.682242, "type": "blade", "hash": "bladeH:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.phptemplates.auvista.blocks.footer", "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fresources%2Fviews%2Ftemplates%2Fauvista%2Fblocks%2Ffooter.blade.php&line=1", "ajax": false, "filename": "footer.blade.php", "line": "?"}}]}, "queries": {"count": 52, "nb_statements": 52, "nb_visible_statements": 52, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.044069999999999984, "accumulated_duration_str": "44.07ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `static_pages` where `slug` = 'thuong-hieu' and `lang` = 'vi' and `status` = 'published' limit 1", "type": "query", "params": [], "bindings": ["thuong-hieu", "vi", "published"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/StaticPageController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\StaticPageController.php", "line": 437}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.537116, "duration": 0.0029300000000000003, "duration_str": "2.93ms", "memory": 0, "memory_str": null, "filename": "StaticPageController.php:437", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/StaticPageController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\StaticPageController.php", "line": 437}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FStaticPageController.php&line=437", "ajax": false, "filename": "StaticPageController.php", "line": "437"}, "connection": "auvista", "explain": null, "start_percent": 0, "width_percent": 6.649}, {"sql": "select * from `users` where `users`.`id` = 1 limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "app/Http/Controllers/StaticPageController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\StaticPageController.php", "line": 444}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.548553, "duration": 0.0016, "duration_str": "1.6ms", "memory": 0, "memory_str": null, "filename": "StaticPageController.php:444", "source": {"index": 22, "namespace": null, "name": "app/Http/Controllers/StaticPageController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\StaticPageController.php", "line": 444}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FStaticPageController.php&line=444", "ajax": false, "filename": "StaticPageController.php", "line": "444"}, "connection": "auvista", "explain": null, "start_percent": 6.649, "width_percent": 3.631}, {"sql": "select count(*) as aggregate from `brands`", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/StaticPageController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\StaticPageController.php", "line": 455}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.554192, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "StaticPageController.php:455", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/StaticPageController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\StaticPageController.php", "line": 455}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FStaticPageController.php&line=455", "ajax": false, "filename": "StaticPageController.php", "line": "455"}, "connection": "auvista", "explain": null, "start_percent": 10.279, "width_percent": 1.815}, {"sql": "select * from `brands` limit 20 offset 0", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 19, "namespace": null, "name": "app/Http/Controllers/StaticPageController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\StaticPageController.php", "line": 455}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 44}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 266}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 212}], "start": **********.555595, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "StaticPageController.php:455", "source": {"index": 19, "namespace": null, "name": "app/Http/Controllers/StaticPageController.php", "file": "H:\\laragon\\www\\auvista\\app\\Http\\Controllers\\StaticPageController.php", "line": 455}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FStaticPageController.php&line=455", "ajax": false, "filename": "StaticPageController.php", "line": "455"}, "connection": "auvista", "explain": null, "start_percent": 12.094, "width_percent": 1.18}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.568126, "duration": 0.0008399999999999999, "duration_str": "840μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 13.274, "width_percent": 1.906}, {"sql": "select * from `media` where `media`.`model_id` in (2) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.583124, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 15.18, "width_percent": 1.725}, {"sql": "select * from `media` where `media`.`model_id` in (3) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.584954, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 16.905, "width_percent": 1.543}, {"sql": "select * from `media` where `media`.`model_id` in (4) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.5865211, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 18.448, "width_percent": 1.066}, {"sql": "select * from `media` where `media`.`model_id` in (5) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.587826, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 19.514, "width_percent": 1.135}, {"sql": "select * from `media` where `media`.`model_id` in (6) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.5891721, "duration": 0.0005, "duration_str": "500μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 20.649, "width_percent": 1.135}, {"sql": "select * from `media` where `media`.`model_id` in (7) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.590891, "duration": 0.00145, "duration_str": "1.45ms", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 21.784, "width_percent": 3.29}, {"sql": "select * from `media` where `media`.`model_id` in (8) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.5936232, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 25.074, "width_percent": 1.838}, {"sql": "select * from `media` where `media`.`model_id` in (9) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.595306, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 26.912, "width_percent": 1.384}, {"sql": "select * from `media` where `media`.`model_id` in (10) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.596798, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 28.296, "width_percent": 1.679}, {"sql": "select * from `media` where `media`.`model_id` in (11) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.598774, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 29.975, "width_percent": 1.679}, {"sql": "select * from `media` where `media`.`model_id` in (12) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.600413, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 31.654, "width_percent": 1.543}, {"sql": "select * from `media` where `media`.`model_id` in (13) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.602058, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 33.197, "width_percent": 1.747}, {"sql": "select * from `media` where `media`.`model_id` in (14) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.6037018, "duration": 0.0006, "duration_str": "600μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 34.944, "width_percent": 1.361}, {"sql": "select * from `media` where `media`.`model_id` in (15) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.605335, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 36.306, "width_percent": 1.974}, {"sql": "select * from `media` where `media`.`model_id` in (16) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.6071272, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 38.28, "width_percent": 1.407}, {"sql": "select * from `media` where `media`.`model_id` in (17) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.608613, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 39.687, "width_percent": 1.293}, {"sql": "select * from `media` where `media`.`model_id` in (18) and `media`.`model_type` = 'App\\\\Models\\\\Brand'", "type": "query", "params": [], "bindings": ["App\\Models\\Brand"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 27}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 279}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 311}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 298}], "start": **********.60993, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:635", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "H:\\laragon\\www\\auvista\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 635}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=635", "ajax": false, "filename": "InteractsWithMedia.php", "line": "635"}, "connection": "auvista", "explain": null, "start_percent": 40.98, "width_percent": 1.407}, {"sql": "select * from `settings` where `name` = 'site_logo' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_logo", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.617482, "duration": 0.00155, "duration_str": "1.55ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 42.387, "width_percent": 3.517}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.6246512, "duration": 0.00117, "duration_str": "1.17ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 45.904, "width_percent": 2.655}, {"sql": "select * from `menus` where `location` = 'main-menu' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["main-menu", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.63958, "duration": 0.00177, "duration_str": "1.77ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 48.559, "width_percent": 4.016}, {"sql": "select * from `menu_items` where `menu_id` = 5 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [5, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.6429038, "duration": 0.00113, "duration_str": "1.13ms", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 52.575, "width_percent": 2.564}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (17, 18, 28, 29, 30, 31, 32, 67) and `status` = 1 order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 21, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.645616, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 55.14, "width_percent": 2.201}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (19, 24, 25, 26, 27) and `status` = 1 order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 25, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 26, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 30, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.647519, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 25, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 57.341, "width_percent": 1.838}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (20, 21, 22, 23) and `status` = 1 order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 30, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 31, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 35, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.649082, "duration": 0.00068, "duration_str": "680μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 30, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 59.179, "width_percent": 1.543}, {"sql": "select * from `brands` where `brands`.`id` = 5 limit 1", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, {"index": 27, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 32}, {"index": 32, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 30}, {"index": 33, "namespace": "view", "name": "templates.auvista.blocks.header", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/header.blade.php", "line": 142}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}], "start": **********.651587, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "MenuItem.php:72", "source": {"index": 21, "namespace": null, "name": "app/Models/MenuItem.php", "file": "H:\\laragon\\www\\auvista\\app\\Models\\MenuItem.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=72", "ajax": false, "filename": "MenuItem.php", "line": "72"}, "connection": "auvista", "explain": null, "start_percent": 60.722, "width_percent": 1.634}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.654532, "duration": 0.00149, "duration_str": "1.49ms", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 62.355, "width_percent": 3.381}, {"sql": "select * from `products` where `products`.`id` = 12 limit 1", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Livewire/CartPopup.php", "file": "H:\\laragon\\www\\auvista\\app\\Livewire\\CartPopup.php", "line": 66}, {"index": 21, "namespace": null, "name": "app/Livewire/CartPopup.php", "file": "H:\\laragon\\www\\auvista\\app\\Livewire\\CartPopup.php", "line": 38}, {"index": 22, "namespace": null, "name": "app/Livewire/CartPopup.php", "file": "H:\\laragon\\www\\auvista\\app\\Livewire\\CartPopup.php", "line": 27}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.663626, "duration": 0.00111, "duration_str": "1.11ms", "memory": 0, "memory_str": null, "filename": "CartPopup.php:66", "source": {"index": 20, "namespace": null, "name": "app/Livewire/CartPopup.php", "file": "H:\\laragon\\www\\auvista\\app\\Livewire\\CartPopup.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FLivewire%2FCartPopup.php&line=66", "ajax": false, "filename": "CartPopup.php", "line": "66"}, "connection": "auvista", "explain": null, "start_percent": 65.736, "width_percent": 2.519}, {"sql": "select * from `products` where `sku` = 'AVP-H5USPS' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["AVP-H5USPS", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Livewire/CartPopup.php", "file": "H:\\laragon\\www\\auvista\\app\\Livewire\\CartPopup.php", "line": 73}, {"index": 17, "namespace": null, "name": "app/Livewire/CartPopup.php", "file": "H:\\laragon\\www\\auvista\\app\\Livewire\\CartPopup.php", "line": 38}, {"index": 18, "namespace": null, "name": "app/Livewire/CartPopup.php", "file": "H:\\laragon\\www\\auvista\\app\\Livewire\\CartPopup.php", "line": 27}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/BoundMethod.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Container/Util.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 43}], "start": **********.6654952, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "CartPopup.php:73", "source": {"index": 16, "namespace": null, "name": "app/Livewire/CartPopup.php", "file": "H:\\laragon\\www\\auvista\\app\\Livewire\\CartPopup.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FLivewire%2FCartPopup.php&line=73", "ajax": false, "filename": "CartPopup.php", "line": "73"}, "connection": "auvista", "explain": null, "start_percent": 68.255, "width_percent": 1.52}, {"sql": "select * from `products` where `id` in (12)", "type": "query", "params": [], "bindings": [12], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": "view", "name": "b5e43f9817fe4e8bd863647ff7d7152e", "file": "H:\\laragon\\www\\auvista\\storage\\framework\\views\\b5e43f9817fe4e8bd863647ff7d7152e.php", "line": 19}, {"index": 17, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 19, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.673637, "duration": 0.00103, "duration_str": "1.03ms", "memory": 0, "memory_str": null, "filename": "b5e43f9817fe4e8bd863647ff7d7152e:19", "source": {"index": 15, "namespace": "view", "name": "b5e43f9817fe4e8bd863647ff7d7152e", "file": "H:\\laragon\\www\\auvista\\storage\\framework\\views\\b5e43f9817fe4e8bd863647ff7d7152e.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Fb5e43f9817fe4e8bd863647ff7d7152e.php&line=19", "ajax": false, "filename": "b5e43f9817fe4e8bd863647ff7d7152e.php", "line": "19"}, "connection": "auvista", "explain": null, "start_percent": 69.775, "width_percent": 2.337}, {"sql": "select * from `products` where `sku` = 'AVP-H5USPS' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["AVP-H5USPS", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": "view", "name": "b5e43f9817fe4e8bd863647ff7d7152e", "file": "H:\\laragon\\www\\auvista\\storage\\framework\\views\\b5e43f9817fe4e8bd863647ff7d7152e.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 38}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php", "line": 75}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 16}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/View.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 209}], "start": **********.675487, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "b5e43f9817fe4e8bd863647ff7d7152e:32", "source": {"index": 16, "namespace": "view", "name": "b5e43f9817fe4e8bd863647ff7d7152e", "file": "H:\\laragon\\www\\auvista\\storage\\framework\\views\\b5e43f9817fe4e8bd863647ff7d7152e.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fstorage%2Fframework%2Fviews%2Fb5e43f9817fe4e8bd863647ff7d7152e.php&line=32", "ajax": false, "filename": "b5e43f9817fe4e8bd863647ff7d7152e.php", "line": "32"}, "connection": "auvista", "explain": null, "start_percent": 72.113, "width_percent": 1.43}, {"sql": "select * from `settings` where `name` = 'site_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.683171, "duration": 0.00092, "duration_str": "920μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 73.542, "width_percent": 2.088}, {"sql": "select * from `settings` where `name` = 'company_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["company_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.68507, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 75.63, "width_percent": 1.929}, {"sql": "select * from `settings` where `name` = 'company_name' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["company_name", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.686775, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "helpers.php:21", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 21}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=21", "ajax": false, "filename": "helpers.php", "line": "21"}, "connection": "auvista", "explain": null, "start_percent": 77.558, "width_percent": 1.702}, {"sql": "select * from `settings` where `name` = 'contact_address' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["contact_address", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.688351, "duration": 0.00074, "duration_str": "740μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 79.26, "width_percent": 1.679}, {"sql": "select * from `settings` where `name` = 'office_address' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["office_address", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.689795, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 80.939, "width_percent": 1.066}, {"sql": "select * from `settings` where `name` = 'contact_email' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["contact_email", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.690973, "duration": 0.00047, "duration_str": "470μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 82.006, "width_percent": 1.066}, {"sql": "select * from `menus` where `location` = 'footer-support' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["footer-support", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.692195, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 83.072, "width_percent": 1.112}, {"sql": "select * from `menu_items` where `menu_id` = 6 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [6, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.6934211, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 84.184, "width_percent": 1.951}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (33, 34, 35) and `status` = 1 order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 21, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 81}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.695051, "duration": 0.0008900000000000001, "duration_str": "890μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 86.136, "width_percent": 2.02}, {"sql": "select * from `menus` where `location` = 'footer-policy' and `lang` = 'vi' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["footer-policy", "vi", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, {"index": 17, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 21, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.6967568, "duration": 0.00057, "duration_str": "570μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:18", "source": {"index": 16, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=18", "ajax": false, "filename": "MenuHelper.php", "line": "18"}, "connection": "auvista", "explain": null, "start_percent": 88.155, "width_percent": 1.293}, {"sql": "select * from `menu_items` where `menu_id` = 7 and `parent_id` is null and `status` = 1 order by `order` asc", "type": "query", "params": [], "bindings": [7, 1], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 16, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.697923, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 15, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 89.449, "width_percent": 1.611}, {"sql": "select * from `menu_items` where `menu_items`.`parent_id` in (36, 37, 38, 39, 40) and `status` = 1 order by `order` asc, `order` asc", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, {"index": 21, "namespace": "view", "name": "templates.auvista.blocks.footer", "file": "H:\\laragon\\www\\auvista\\resources\\views/templates/auvista/blocks/footer.blade.php", "line": 134}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 25, "namespace": null, "name": "vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php", "line": 22}], "start": **********.699413, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "MenuHelper.php:29", "source": {"index": 20, "namespace": null, "name": "app/Helpers/MenuHelper.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\MenuHelper.php", "line": 29}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2FMenuHelper.php&line=29", "ajax": false, "filename": "MenuHelper.php", "line": "29"}, "connection": "auvista", "explain": null, "start_percent": 91.06, "width_percent": 1.974}, {"sql": "select * from `settings` where `name` = 'facebook_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["facebook_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.702199, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 93.034, "width_percent": 1.702}, {"sql": "select * from `settings` where `name` = 'instagram_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["instagram_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.7037299, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 94.736, "width_percent": 1.384}, {"sql": "select * from `settings` where `name` = 'youtube_url' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["youtube_url", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.704972, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 96.12, "width_percent": 1.112}, {"sql": "select * from `settings` where `name` = 'site_copyright' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["site_copyright", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.706094, "duration": 0.00066, "duration_str": "660μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 97.232, "width_percent": 1.498}, {"sql": "select * from `settings` where `name` = 'designer_credit' and `lang` = 'vi' limit 1", "type": "query", "params": [], "bindings": ["designer_credit", "vi"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Cache/CacheManager.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Cache\\CacheManager.php", "line": 453}, {"index": 20, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 16}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 124}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php", "file": "H:\\laragon\\www\\auvista\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}], "start": **********.707469, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "helpers.php:17", "source": {"index": 16, "namespace": null, "name": "app/Helpers/helpers.php", "file": "H:\\laragon\\www\\auvista\\app\\Helpers\\helpers.php", "line": 17}, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHelpers%2Fhelpers.php&line=17", "ajax": false, "filename": "helpers.php", "line": "17"}, "connection": "auvista", "explain": null, "start_percent": 98.729, "width_percent": 1.271}]}, "models": {"data": {"App\\Models\\MenuItem": {"value": 25, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenuItem.php&line=1", "ajax": false, "filename": "MenuItem.php", "line": "?"}}, "App\\Models\\Brand": {"value": 19, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FBrand.php&line=1", "ajax": false, "filename": "Brand.php", "line": "?"}}, "App\\Models\\Setting": {"value": 12, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FSetting.php&line=1", "ajax": false, "filename": "Setting.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}, "App\\Models\\Menu": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FMenu.php&line=1", "ajax": false, "filename": "Menu.php", "line": "?"}}, "App\\Models\\Product": {"value": 2, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FProduct.php&line=1", "ajax": false, "filename": "Product.php", "line": "?"}}, "App\\Models\\StaticPage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FStaticPage.php&line=1", "ajax": false, "filename": "StaticPage.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 73, "is_counter": true}, "livewire": {"data": {"cart-count #5QEs30MnfElWEmEQbx5J": "array:4 [\n  \"data\" => array:1 [\n    \"count\" => 1\n  ]\n  \"name\" => \"cart-count\"\n  \"component\" => \"App\\Livewire\\CartCount\"\n  \"id\" => \"5QEs30MnfElWEmEQbx5J\"\n]", "cart-count #aGYwfsRIIoS29PYMX03X": "array:4 [\n  \"data\" => array:1 [\n    \"count\" => 1\n  ]\n  \"name\" => \"cart-count\"\n  \"component\" => \"App\\Livewire\\CartCount\"\n  \"id\" => \"aGYwfsRIIoS29PYMX03X\"\n]", "cart-popup #QQag4tXH7Ryez24J0uy7": "array:4 [\n  \"data\" => array:2 [\n    \"cartItems\" => array:1 [\n      12 => 1\n    ]\n    \"total\" => 350000000.0\n  ]\n  \"name\" => \"cart-popup\"\n  \"component\" => \"App\\Livewire\\CartPopup\"\n  \"id\" => \"QQag4tXH7Ryez24J0uy7\"\n]"}, "count": 3}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "https://auvista.test/thuong-hieu", "action_name": "page.brand", "controller_action": "App\\Http\\Controllers\\StaticPageController@brand", "uri": "GET thuong-hieu", "controller": "App\\Http\\Controllers\\StaticPageController@brand<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FStaticPageController.php&line=431\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=H%3A%2Flaragon%2Fwww%2Fauvista%2Fapp%2FHttp%2FControllers%2FStaticPageController.php&line=431\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/StaticPageController.php:431-475</a>", "middleware": "web", "duration": "975ms", "peak_memory": "46MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1295647551 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1295647551\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-537558738 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-537558738\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1251580436 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"12 characters\">auvista.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"64 characters\">&quot;Not)A;Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;138&quot;, &quot;Google Chrome&quot;;v=&quot;138&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">https://auvista.test/thuong-hieu</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">vi-VN,vi;q=0.9,fr-FR;q=0.8,fr;q=0.7,en-US;q=0.6,en;q=0.5</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"713 characters\">XSRF-TOKEN=eyJpdiI6IktJaW9xUGc1UEd4UzgvSXlnN1BvRXc9PSIsInZhbHVlIjoiZTRiNHI4dEFzRzc0cmNMWk5CNjJ0SkFPMHdFdkpWcjRoOXNZVHFtNUcxUVBNMjcvMjR4UzVFaEVZVGQrT3lOZW90c1hqVWR6dzBaODRjQlo0ZkdyMmNlMklFSnJGK0hUVjBCditRcmt2TDQ5VGZ1V3c3RStVWENZSXNCdXZYRDIiLCJtYWMiOiJkZGQ0NGE2Y2NmNDgyY2RlNWE1YzUyMjNiMDBkOThmYTU4YTc0NDlkMTQzYTA0MzRjZjM0ZmIwYjVhNzNmNGU5IiwidGFnIjoiIn0%3D; laravel_session=eyJpdiI6IkZyRWVwYkY3OVRCdExnYnpBSnMrMVE9PSIsInZhbHVlIjoiMXV3TklCWExvTlNiOHBQK3ZLUXJwRC82OGtieTNLckNBVUFaRXFlcTY3V0N6VHlNUm5sVGwxVU1jOGd6ZWZGOUdheUZHdGhrdG95YWZSbmt3TGJ6ZlI5MFVHTDdhaVhxaFRTZzlIMmtsQkJIc2JyN09oclRWZktZRkxXWCtTNmoiLCJtYWMiOiIzOWExODM0YTY5NGJkOWM0M2E5YTExM2Q5YjBlMzBjMmMzYjQ2YzdlNTNkMWJmMzAwNTY4MzVmZTA1MmI2MTlhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1251580436\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1130703298 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x8s4D1dVLNtZ1F4esDP7W7ogBP4qelaw71gjvUmS</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">y6qoLsHnV2DFsQpcZMnA28moh7ZYgBrNAG7wtpmZ</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1130703298\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-846120885 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 17 Jul 2025 09:28:51 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-846120885\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-376878766 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">x8s4D1dVLNtZ1F4esDP7W7ogBP4qelaw71gjvUmS</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"32 characters\">https://auvista.test/thuong-hieu</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>cart</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-key>12</span> => <span class=sf-dump-num>1</span>\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-376878766\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://auvista.test/thuong-hieu", "action_name": "page.brand", "controller_action": "App\\Http\\Controllers\\StaticPageController@brand"}, "badge": null}}