<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Config;
use Illuminate\Database\QueryException;
use Illuminate\Support\Facades\DB;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        // Tắt kiểm tra khóa ngoại
        DB::statement('SET FOREIGN_KEY_CHECKS=0');

        // Tạm thời tắt cache để tránh lỗi khi chạy migration
        $originalCacheDriver = Config::get('cache.default');
        Config::set('cache.default', 'array');

        try {
            // Create default roles if they don't exist
            $roles = [
                'super-admin' => 'Super Admin',
                'editor' => 'Biên tập',
                'viewer' => 'Khách xem'
            ];

            foreach ($roles as $key => $name) {
                Role::firstOrCreate(['name' => $key], ['name' => $key]);
            }

            // Get the super-admin role
            $superAdmin = Role::where('name', 'super-admin')->first();

            // Create Admin User if not exists
            $admin = User::firstOrCreate(
                ['id' => 1],
                [
                    'email' => '<EMAIL>',
                    'name' => 'Admin',
                    'password' => Hash::make('9QlmtR5XNgB0Wcy'),
                    'email_verified_at' => now(),
                    'user_type' => 'admin',
                ]
            );

            // Assign Super Admin Role to Admin if not already assigned
            if (!$admin->hasRole('super-admin')) {
                $admin->assignRole($superAdmin);
            }

            // Chạy các seeder cho các module khác nhau
            $seeders = [
                'BannerSeeder' => Schema::hasTable('banners'),
                'SettingSeeder' => Schema::hasTable('settings'),
                'FooterMenuSeeder' => Schema::hasTable('menus'),
                'StaticPageSeeder' => Schema::hasTable('static_pages'),
                'PermissionSeeder' => true, // Không phụ thuộc vào bảng cụ thể
                'MenuSeeder' => Schema::hasTable('menus'),
                'CategorySeeder' => Schema::hasTable('categories'),
                'BrandSeeder' => Schema::hasTable('brands'),
                'ProductSeeder' => Schema::hasTable('products'),
                'PostSeeder' => Schema::hasTable('posts'),
                //'OrderSeeder' => Schema::hasTable('orders'),
                //'ProductReviewSeeder' => Schema::hasTable('product_reviews'),
                //'ProductPromotionSeeder' => Schema::hasTable('product_promotions'),
            ];

            foreach ($seeders as $seeder => $shouldRun) {
                if ($shouldRun) {
                    $this->call("Database\\Seeders\\$seeder");
                } else {
                    $this->command->warn("Bỏ qua $seeder vì bảng dữ liệu chưa tồn tại.");
                }
            }
        } catch (QueryException $e) {
            $this->command->error('Lỗi cơ sở dữ liệu: ' . $e->getMessage());
        } catch (\Exception $e) {
            $this->command->error('Lỗi không xác định: ' . $e->getMessage());
        } finally {
            // Khôi phục cấu hình cache
            Config::set('cache.default', $originalCacheDriver);
        }


        // Bật lại kiểm tra khóa ngoại
        DB::statement('SET FOREIGN_KEY_CHECKS=1');
    }
}
